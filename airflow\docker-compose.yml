version: '3'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data

  webserver:
    build: .
    restart: always
    depends_on:
      - postgres
    environment:
      AIRFLOW__CORE__EXECUTOR: LocalExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
      AIRFLOW__WEBSERVER__RBAC: "True"
      AIRFLOW__WEBSERVER__AUTH_BACKEND: airflow.www.security.AirflowSecurityManager
      AIRFLOW__API__AUTH_BACKEND: "airflow.api.auth.backend.session"
    volumes:
      - ./dags:/opt/airflow/dags
      - ./webserver_config.py:/opt/airflow/webserver_config.py
    ports:
      - "8080:8080"
    command: webserver

  scheduler:
    build: .
    restart: always
    depends_on:
      - postgres
    environment:
      AIRFLOW__CORE__EXECUTOR: LocalExecutor
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    volumes:
      - ./dags:/opt/airflow/dags
    command: scheduler

volumes:
  postgres-db-volume:
