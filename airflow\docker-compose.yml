version: '3.8'

x-airflow-common: &airflow-common
  image: databricks-airflow:latest
  build:
    context: .
    dockerfile: Dockerfile
  environment: &airflow-common-env
    AIRFLOW__CORE__EXECUTOR: CeleryExecutor
    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:${POSTGRES_PASSWORD:-airflow123}@postgres/airflow
    AIRFLOW__CELERY__BROKER_URL: redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
    AIRFLOW__CELERY__RESULT_BACKEND: db+postgresql://airflow:${POSTGRES_PASSWORD:-airflow123}@postgres/airflow
    AIRFLOW__CORE__FERNET_KEY: ${AIRFLOW_FERNET_KEY:-}
    AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
    AIRFLOW__WEBSERVER__EXPOSE_CONFIG: 'false'
    AIRFLOW__WEBSERVER__RBAC: 'true'
    AIRFLOW__WEBSERVER__SECRET_KEY: ${AIRFLOW_SECRET_KEY:-}
    AIRFLOW__CORE__SECURE_MODE: 'true'
    AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session'
    AIRFLOW_CONN_SNOWFLAKE: snowflake://${DBT_SNOWFLAKE_USER:-x4_dw_programing_user}:${DBT_SNOWFLAKE_PASSWORD:-hCBcEuNzCR7fWonF8IYEq2PGYZ}@${DBT_SNOWFLAKE_ACCOUNT:-a2813805280961-x4}.snowflakecomputing.com?account=${DBT_SNOWFLAKE_ACCOUNT:-a2813805280961-x4}&database=${DBT_SNOWFLAKE_DATABASE:-X4_DW_DEV_V2}&warehouse=${DBT_SNOWFLAKE_WAREHOUSE:-DW_WH}&role=${DBT_SNOWFLAKE_ROLE:-ACCOUNTADMIN}&schema=${DBT_SNOWFLAKE_SCHEMA:-DATAMART}
    AIRFLOW_VAR_DBT_PROJECT_DIR: /opt/airflow/dbt
    AIRFLOW_VAR_ENVIRONMENT: ${ENVIRONMENT:-dev}
    AIRFLOW_VAR_SLACK_CHANNEL: ${SLACK_CHANNEL:-#data-alerts}
    AIRFLOW_VAR_SLACK_WEBHOOK_TOKEN: ${SLACK_WEBHOOK_TOKEN:-}
    AIRFLOW_CONN_AWS_DEFAULT: aws://${AWS_ACCESS_KEY_ID:-}:${AWS_SECRET_ACCESS_KEY:-}@?region_name=us-east-1
  volumes:
    - ./dags:/opt/airflow/dags
    - ./logs:/opt/airflow/logs
  depends_on:
    postgres:
      condition: service_healthy
  networks:
    - airflow-network
  restart: always
  healthcheck:
    test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
    interval: 30s
    timeout: 10s
    retries: 5
    start_period: 30s

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-airflow}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-airflow123}
      POSTGRES_DB: ${POSTGRES_DB:-airflow}
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    networks:
      - airflow-network
    restart: always
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    networks:
      - airflow-network
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-redis123}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  airflow-webserver:
    <<: *airflow-common
    command: webserver
    ports:
      - "8080:8080"
    environment:
      <<: *airflow-common-env
    depends_on:
      - airflow-init
      - redis
      - postgres

  airflow-scheduler:
    <<: *airflow-common
    command: scheduler
    environment:
      <<: *airflow-common-env
    depends_on:
      - airflow-init
      - redis
      - postgres

  airflow-worker:
    <<: *airflow-common
    command: celery worker
    environment:
      <<: *airflow-common-env
    depends_on:
      - airflow-scheduler
      - redis
      - postgres

  airflow-init:
    <<: *airflow-common
    entrypoint: /bin/bash
    command:
      - -c
      - |
        mkdir -p /sources/logs /sources/dags /sources/plugins /sources/dbt
        chown -R "${AIRFLOW_UID:-50000}:0" /sources/{logs,dags,plugins,dbt}
        exec airflow db init && \
        airflow users create \
          --username admin \
          --firstname Admin \
          --lastname User \
          --role Admin \
          --email <EMAIL> \
          --password ${AIRFLOW_ADMIN_PASSWORD:-admin123}
    environment:
      <<: *airflow-common-env
    depends_on:
      - postgres
      - redis
    networks:
      - airflow-network

networks:
  airflow-network:
    driver: bridge

volumes:
  postgres-db-volume:
