from airflow.www.security import AirflowSecurityManager
from flask_appbuilder.security.manager import AUTH_OAUTH
import os
import logging

# Configure logging
log = logging.getLogger(__name__)

# Authentication configuration
AUTH_TYPE = AUTH_OAUTH
AUTH_ROLE_ADMIN = 'Admin'
AUTH_USER_REGISTRATION = True
AUTH_USER_REGISTRATION_ROLE = "Viewer"

# OAuth configuration
OAUTH_PROVIDERS = [{
    'name': 'google',
    'token_key': 'access_token',
    'icon': 'fa-google',
    'remote_app': {
        'client_id': '***********-jk30san9bs04fmim7ui3d9vcfde0ukvj.apps.googleusercontent.com',
        'client_secret': '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',
        'api_base_url': 'https://www.googleapis.com/oauth2/v2/',
        'client_kwargs': {
            'scope': 'openid email profile'
        },
        'server_metadata_url': 'https://accounts.google.com/.well-known/openid_configuration',
        'request_token_url': None,
        'access_token_url': 'https://oauth2.googleapis.com/token',
        'authorize_url': 'https://accounts.google.com/o/oauth2/auth',
    }
}]

# Custom Security Manager
class CustomSecurityManager(AirflowSecurityManager):
    def oauth_user_info(self, provider, response=None):  # pylint: disable=unused-argument
        """
        Retrieves the user info from Google OAuth
        """
        if provider == 'google':
            try:
                me = self.appbuilder.sm.oauth_remotes[provider].get('userinfo')
                log.info("User info from Google: %s", me.json())
                user_data = me.json()
                return {
                    'username': user_data.get('email', ''),
                    'first_name': user_data.get('given_name', ''),
                    'last_name': user_data.get('family_name', ''),
                    'email': user_data.get('email', ''),
                }
            except Exception as e:
                log.error("Error getting user info from Google: %s", str(e))
                return {}
        return {}

# Set the custom security manager
SECURITY_MANAGER_CLASS = CustomSecurityManager
