from airflow.www.security import AirflowSecurityManager
from flask_appbuilder.security.manager import AUTH_OAUTH
import os
import logging

# Configure logging
log = logging.getLogger(__name__)

# Authentication configuration
AUTH_TYPE = AUTH_OAUTH
AUTH_ROLE_ADMIN = 'Admin'
AUTH_USER_REGISTRATION = True
AUTH_USER_REGISTRATION_ROLE = "Viewer"

# OAuth configuration
OAUTH_PROVIDERS = [{
    'name': 'google',
    'token_key': 'access_token',
    'icon': 'fa-google',
    'remote_app': {
        'client_id': '***********-jk30san9bs04fmim7ui3d9vcfde0ukvj.apps.googleusercontent.com',
        'client_secret': os.getenv("GOOGLE_OAUTH_CLIENT_SECRET"),
        'api_base_url': 'https://www.googleapis.com/oauth2/v2/',
        'client_kwargs': {
            'scope': 'email profile'
        },
        'request_token_url': None,
        'access_token_url': 'https://oauth2.googleapis.com/token',
        'authorize_url': 'https://accounts.google.com/o/oauth2/auth',
        'server_metadata_url': 'https://accounts.google.com/.well-known/openid_configuration',
    }
}]

# Custom Security Manager
class CustomSecurityManager(AirflowSecurityManager):
    def oauth_user_info(self, provider, response=None):  # pylint: disable=unused-argument
        """
        Retrieves the user info from Google OAuth
        """
        if provider == 'google':
            me = self.appbuilder.sm.oauth_remotes[provider].get('userinfo')
            log.debug("User info from Google: %s", me.json())
            return {
                'username': me.json().get('email', ''),
                'first_name': me.json().get('given_name', ''),
                'last_name': me.json().get('family_name', ''),
                'email': me.json().get('email', ''),
            }
        return {}

# Set the custom security manager
SECURITY_MANAGER_CLASS = CustomSecurityManager
