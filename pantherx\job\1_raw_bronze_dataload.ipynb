{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1ac2c89c-d37c-4b8c-99cd-50a925368b28", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from azure.storage.blob import BlobServiceClient\n", "from datetime import timezone\n", "import re\n", "from pyspark.sql.functions import lit, current_timestamp\n", "from pyspark.sql import DataFrame\n", "\n", "connection_string = \"DefaultEndpointsProtocol=https;AccountName=naralladatastorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net\" \n", "container_name = \"dev\"\n", "prefix = \"data/\"\n", "pattern = re.compile(r\"PNT_PRG_Status_20[^/]*$\")\n", "\n", "blob_service_client = BlobServiceClient.from_connection_string(connection_string)\n", "container_client = blob_service_client.get_container_client(container_name)\n", "\n", "combined_df: DataFrame = None\n", "source_system_id = spark.sql(\"select id from dwh_utils.source_system_master where systemname = 'pantherx';\").first()['id']\n", "spark.sql(\"delete from bronze.raw_prx_patient_status where date(date_created) = current_date()\")\n", "\n", "# Process files\n", "for blob in container_client.list_blobs(name_starts_with=prefix):\n", "    file_name = blob.name.split(\"/\")[-1]\n", "    if pattern.match(file_name):\n", "        file_name = file_name\n", "        blob_path= f\"{container_name}/{blob.name}\"\n", "        blob_file_upload_time= blob.last_modified.astimezone(timezone.utc).isoformat()\n", "        archive_blob_path = f'{container_name}/Archive'\n", "        spark.sql(f\"insert into dwh_utils.data_source_tracker(FIL<PERSON>AM<PERSON>,FILEPATH,ARCHIVE_FILEPATH,STATUS,ERROR_MESSAGE,BLOB_FILE_UPLOAD_TIME,SOURCE_SYSTEM_ID) values ('{file_name}','{blob_path}','{archive_blob_path}','PROCESSING','','{blob_file_upload_time}',{source_system_id});\")\n", "        datasource_id =spark.sql(f\"select max(id) as id from dwh_utils.data_source_tracker where filename = '{file_name}' and STATUS = 'PROCESSING' and SOURCE_SYSTEM_ID = {source_system_id};\").first()['id']\n", "\n", "        df = spark.read \\\n", "            .option(\"header\", \"true\") \\\n", "            .option(\"delimiter\", \"|\") \\\n", "            .option(\"multiline\", \"false\") \\\n", "            .option(\"escape\", '\"') \\\n", "            .option(\"quote\", '\"') \\\n", "            .option(\"inferSchema\", \"false\") \\\n", "            .csv(f\"/mnt/{blob_path}\")\n", "        \n", "        df = df.toDF(*[col.strip().upper().replace(\" \", \"_\") for col in df.columns])\n", "        df = df.withColumn(\"DATASOURCE_ID\", lit(datasource_id)) \\\n", "       .withColumn(\"DATE_CREATED\", current_timestamp())\n", "        \n", "\n", "        df = df.withColumnRenamed(\"PRESCRIBER DEA\", \"PRESCRIBER_DEA\")\n", "        df = df.withColumnRenamed(\"TRANSACTION_SEQUENCE_NO\", \"TRANSACTION_SEQUENCE\")\n", "        df = df.withColumnRenamed(\"PRESCRIBER_SPEC\", \"PRESCRIBER_SPECIALTY\")\n", "        df = df.withColumnRenamed(\"PRODUCT_EXPIRATION_DATE\", \"PRODUCT_EXP_DATE\")\n", "        df = df.withColumnRenamed(\"PATIENT_ADDR1\", \"PATIENT_ADDRESS_LINE1\")\n", "        df = df.withColumnRenamed(\"PATIENT_ADDR2\", \"PATIENT_ADDRESS_LINE2\")\n", "        df = df.withColumnRenamed(\"SUBSTATUS\", \"SUB_STATUS\")\n", "        df = df.withColumnRenamed(\"PRESCRIBER_LN\", \"PRESCRIBER_LAST_NAME\")\n", "        df = df.withColumnRenamed(\"PRESCRIBER_FN\", \"PRESCRIBER_FIRST_NAME\")\n", "        df = df.withColumnRenamed(\"PRESCRIBER_ADDR1\", \"PRESCRIBER_ADDRESS_LINE1\")\n", "        df = df.withColumnRenamed(\"PRESCRIBER_ADDR2\", \"PRESCRIBER_ADDRESS_LINE2\")\n", "        df = df.withColumnRenamed(\"PRESCRIBER_PHN\", \"PRESCRIBER_PHONE\")\n", "        df = df.withColumnRenamed(\"PATIENT_COPAY_AMOUNT\", \"PATIENT_COPAY_AMT\")\n", "        df = df.withColumnRenamed(\"GENETIC_TEST_RESULTS\", \"GENETIC_TEST_RESULT\")\n", "        \n", "        if combined_df is None:\n", "            combined_df = df\n", "        else:\n", "            combined_df = combined_df.unionByName(df, allowMissingColumns=False)\n", "\n", "\n", "if combined_df is not None:\n", "    spark.conf.set(\"spark.databricks.delta.schema.autoMerge.enabled\", \"true\")\n", "    spark.conf.set(\"spark.databricks.delta.columnMapping.mode\", \"name\")\n", "    combined_df.write.format(\"delta\").mode(\"overwrite\").saveAsTable(\"bronze.raw_prx_patient_status\")\n", "    combined_df.write.format(\"delta\").mode(\"overwrite\").save(\"/mnt/pantherx/bronze/raw_prx_patient_status\")\n", "\n", "    # combined_df.write.format(\"delta\").mode(\"append\").saveAsTable(\"bronze.raw_prx_patient_status\")\n", "    # combined_df.write.format(\"delta\").mode(\"overwrite\").save(\"/mnt/pantherx/bronze/raw_prx_patient_status\")\n", " "]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 5956224898218329, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "1_raw_bronze_dataload", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}