{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ff3457a1-71cd-4134-a647-5944910f50f4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import col\n", "from pyspark.sql import SparkSession\n", "\n", "spark = SparkSession.builder.getOrCreate()\n", "\n", "max_iterations = 5\n", "iteration = 0\n", "\n", "while iteration < max_iterations:\n", "    source_df = spark.sql(\"\"\"\n", "        SELECT \n", "            sp_patient_id, \n", "            transaction_id, \n", "            concat_ws(',', collect_list(error_type)) AS error_type\n", "        FROM dwh_dqm.prx_dqm_view\n", "        WHERE DATE(date_created) = CURRENT_DATE() \n", "          AND is_critical = 'yes'\n", "        GROUP BY sp_patient_id, transaction_id\n", "    \"\"\").filter(\"sp_patient_id IS NOT NULL OR transaction_id IS NOT NULL\")\n", "\n", "    source_df.createOrReplaceTempView(\"non_empty_source\")\n", "\n", "    spark.sql(\"\"\"\n", "        MERGE INTO silver.curated_prx_patient_status AS target\n", "        USING non_empty_source AS source\n", "        ON target.sp_patient_id <=> source.sp_patient_id\n", "           AND target.transaction_id <=> source.transaction_id\n", "           AND DATE(target.date_created) = CURRENT_DATE()\n", "        WHEN MATCHED THEN \n", "          UPDATE SET \n", "            target.IS_PROCESSED = 'FALSE',\n", "            target.PROCESSING_STATUS = 'ERROR',\n", "            target.PROCESSING_ERROR = source.error_type\n", "    \"\"\")\n", "\n", "    remaining_errors = spark.sql(\"\"\"\n", "        SELECT COUNT(*) AS count FROM silver.curated_prx_patient_status\n", "        WHERE IS_PROCESSED != 'FALSE' \n", "          AND PROCESSING_STATUS != 'ERROR'\n", "          AND DATE(date_created) = CURRENT_DATE()\n", "    \"\"\").collect()[0][\"count\"]\n", "\n", "    if remaining_errors == 0:\n", "        break\n", "\n", "    iteration += 1\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 8077766293875561, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "3_dqm_result_updates_on_silver", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}