{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f2ef4277-fb1b-4108-ab1a-4928d04d4f1e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE OR REPLACE VIEW dwh_dqm.prx_dqm_view AS\n", "SELECT\n", "  error_message,\n", "  CONCAT(error_column, ' Validation') AS error_type,\n", "  column_value AS error_value,\n", "  sp_id AS sp_patient_id,\n", "  t_id AS transaction_id,\n", "  date_id AS date_created,\n", "  prxid AS id,\n", "  'pantheRx' AS source,\n", "  'yes' AS is_critical,\n", "  CURRENT_TIMESTAMP() AS error_timestamp\n", "FROM (\n", "  SELECT\n", "    'PRESCRIBER_ZIP contains blank or NULL values or not 5 digit number' AS error_message,\n", "    'PRESCRIBER_ZIP' AS error_column,\n", "    CAST(PRESCRIBER_ZIP AS STRING) AS column_value,\n", "    CAST(sp_patient_id AS STRING) AS sp_id,\n", "    CAST(transaction_id AS STRING) AS t_id,\n", "    date_created AS date_id,\n", "    id AS prxid\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE\n", "    PRESCRIBER_ZIP IS NULL\n", "    OR TRIM(PRESCRIBER_ZIP) = ''\n", "    OR LENGTH(PRESCRIBER_ZIP) <> 5\n", "    OR TRY_CAST(PRESCRIBER_ZIP AS INT) IS NULL\n", "    OR PRESCRIBER_ZIP = '00000'\n", "\n", "  UNION ALL\n", "\n", "  SELECT\n", "    'COPAY_ASSIST_FLAG contains blank or NULL values or not in Y, N, U format',\n", "    'COPAY_ASSIST_FLAG',\n", "    CAST(COPAY_ASSIST_FLAG AS STRING),\n", "    CAST(sp_patient_id AS STRING),\n", "    CAST(transaction_id AS STRING),\n", "    date_created,\n", "    id\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true\n", "    AND (\n", "      COPAY_ASSIST_FLAG IS NULL\n", "      OR TRIM(COPAY_ASSIST_FLAG) = ''\n", "      OR COPAY_ASSIST_FLAG NOT IN ('Y', 'N', 'U')\n", "    )\n", "\n", "  UNION ALL\n", "\n", "  SELECT\n", "  'DIAG_CODE_1 contains blank or NULL values or not in WHIM, Other',\n", "  'DIAG_CODE_1' ,\n", "  CAST(DIAG_CODE_1 AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  date_created,\n", "  id\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true\n", "    AND (\n", "      DIAG_CODE_1 IS NULL\n", "      OR TRIM(DIAG_CODE_1) = ''\n", "      OR DIAG_CODE_1 NOT IN ('WHIM', 'Other')\n", "    )\n", "\n", "  UNION ALL\n", "\n", "  SELECT\n", "  'GENETIC_TEST_COMPLETED contains blank or NULL values or not in Y, N, U format',\n", "  'GENETIC_TEST_COMPLETED',\n", "  CAST(GENETIC_TEST_COMPLETED AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  date_created,\n", "  id\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true\n", "    AND (\n", "      GENETIC_TEST_COMPLETED IS NULL\n", "      OR TRIM(GENETIC_TEST_COMPLETED) = ''\n", "      OR GENETIC_TEST_COMPLETED NOT IN ('Y', 'N', 'U')\n", "    )\n", "\n", "  UNION ALL\n", "\n", "  SELECT \n", "  'HCP_SIGNATURE_ON_NEW_RX contains blank or NULL values or not in Y, N format',\n", "  'HCP_SIGNATURE_ON_NEW_RX',\n", "  cast(HCP_SIGNATURE_ON_NEW_RX as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE \n", "    HCP_SIGNATURE_ON_NEW_RX IS NULL\n", "    OR trim(HCP_SIGNATURE_ON_NEW_RX) = ''\n", "    OR upper(trim(HCP_SIGNATURE_ON_NEW_RX)) NOT IN ('Y', 'N')\n", "\n", "  UNION ALL\n", "\n", "  -- NDC validation: null, blank, not 11-digit, or non-numeric\n", "  SELECT \n", "    'NDC contains blank or NULL values or not in 11 digit format' AS error_message,\n", "    'NDC' AS column_name,\n", "    CAST(NDC AS STRING) AS ndc_value,\n", "    CAST(sp_patient_id AS STRING) AS sp_patient_id,\n", "    CAST(transaction_id AS STRING) AS transaction_id,\n", "    date_created,\n", "    id\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true\n", "    AND (\n", "      NDC IS NULL\n", "      OR TRIM(NDC) = ''\n", "      OR LENGTH(regexp_replace(NDC, '[^0-9]', '')) <> 11\n", "    )\n", "\n", "\n", "\n", "  UNION ALL\n", "\n", "  -- PATIENT_WEIGHT_KG validation: null, blank, or non-numeric\n", "  SELECT \n", "    'PATIENT_WEIGHT_KG contains blank or NULL values or not in Number format',\n", "    'PATIENT_WEIGHT_KG',\n", "    cast(PATIENT_WEIGHT_KG as string),\n", "    cast(sp_patient_id as string),\n", "    cast(transaction_id as string),\n", "    date_created,\n", "    id\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true \n", "    AND (\n", "      PATIENT_WEIGHT_KG IS NULL \n", "      OR trim(PATIENT_WEIGHT_KG) = '' \n", "      OR cast(PATIENT_WEIGHT_KG as double) IS NULL\n", "    )\n", "\n", "\n", "  UNION ALL\n", "\n", "  SELECT \n", "  'PRIMARY_PAYOR_NAME contains blank or NULL values',\n", "  'PRIMARY_PAYOR_NAME',\n", "  cast(t1.PRIMARY_PAYOR_NAME as string),\n", "  cast(t1.sp_patient_id as string),\n", "  cast(t1.transaction_id as string),\n", "  t1.date_created,\n", "  t1.id\n", "  FROM silver.curated_prx_patient_status t1\n", "  LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id\n", "  LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id\n", "  WHERE \n", "    t1.is_processed = true\n", "    AND upper(s1.status) <> 'PENDING'\n", "    AND (\n", "      t1.PRIMARY_PAYOR_NAME IS NULL \n", "      OR trim(t1.PRIMARY_PAYOR_NAME) = ''\n", "    )\n", "\n", "  UNION ALL\n", "\n", "  -- PROGRAM_CONSENT validation\n", "SELECT \n", "  'PROGRAM_CONSENT contains blank or NULL values or not in Y, N, U format',\n", "  'PROGRAM_CONSENT',\n", "  cast(PROGRAM_CONSENT as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND (\n", "    PROGRAM_CONSENT IS NULL \n", "    OR trim(PROGRAM_CONSENT) = '' \n", "    OR PROGRAM_CONSENT NOT IN ('Y', 'N', 'U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- SP_PATIENT_ID format validation\n", "SELECT \n", "  'SP_PATIENT_ID contains blank or NULL values or not in proper format',\n", "  'SP_PATIENT_ID',\n", "  cast(SP_PATIENT_ID as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND (\n", "    SP_PATIENT_ID IS NULL \n", "    OR trim(SP_PATIENT_ID) = ''\n", "    OR NOT regexp_like(SP_PATIENT_ID, '^[a-zA-Z0-9-]+$')\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- STATUS validation\n", "SELECT \n", "  'STATUS contains blank or NULL values or may not from given values',\n", "  'STATUS',\n", "  cast(prx.status as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status prx\n", "WHERE is_processed = true \n", "  AND (\n", "    prx.status IS NULL \n", "    OR trim(prx.status) = '' \n", "    OR prx.status NOT IN (SELECT id FROM dwh_utils.status_catalog)\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- SUB_STATUS validation\n", "SELECT \n", "  'SUB_STATUS contains blank or NULL values or may not from given values',\n", "  'SUB_STATUS',\n", "  cast(prx.sub_status as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status prx\n", "WHERE is_processed = true \n", "  AND (\n", "    prx.sub_status IS NULL \n", "    OR trim(prx.sub_status) = '' \n", "    OR prx.sub_status NOT IN (SELECT id FROM dwh_utils.sub_status_catalog)\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- STATUS_DATE format validation\n", "SELECT \n", "  'STATUS_DATE contains blank or NULL values or not in YYYYMMDDHH24MISS format',\n", "  'STATUS_DATE',\n", "  cast(STATUS_DATE as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND (\n", "    STATUS_DATE IS NULL \n", "    OR trim(STATUS_DATE) = '' \n", "    OR to_timestamp(STATUS_DATE, 'yyyyMMddHHmmss') IS NULL\n", "  )\n", "\n", "UNION ALL\n", "-- Copay Assist Flag = Y but copay_assist_amt is NULL\n", "SELECT \n", "  'Copay Assist Amount should be provided when Copay Assist Flag is \"Y\"',\n", "  'copay_assist_amt',\n", "  cast(copay_assist_amt as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND copay_assist_flag = 'Y'\n", "  AND copay_assist_amt IS NULL\n", "\n", "UNION ALL\n", "\n", "-- Copay Assist Flag is U or N but copay_assist_amt is NOT NULL\n", "SELECT \n", "  'Copay Assist Amount should be NULL when Copay Assist Flag is \"U\" or \"N\"',\n", "  'copay_assist_amt',\n", "  cast(copay_assist_amt as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND copay_assist_flag IN ('U', 'N')\n", "  AND copay_assist_amt IS NOT NULL\n", "\n", "UNION ALL\n", "\n", "-- date_shipped is not in valid YYYYMMDD format\n", "SELECT \n", "  'date_shipped must be in the format YYYYMMDD',\n", "  'date_shipped',\n", "  cast(date_shipped as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND date_shipped IS NOT NULL\n", "  AND (\n", "    length(date_shipped) <> 8 \n", "    OR to_date(date_shipped, 'yyyyMMdd') IS NULL\n", "  )\n", "UNION ALL\n", "-- Validation: date_shipped must not be blank for ACTIVE/SHIPMENT or ACTIVE/MATERIALS\n", "SELECT \n", "  'date_shipped must be available and not blank when the staus/substatus combination is ACTIVE/SHIPMENT or ACTIVE/MATERIALS',\n", "  'date_shipped',\n", "  cast(date_shipped as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM (\n", "  SELECT t2.date_shipped, s3.status, s4.sub_status, t2.is_processed, t2.sp_patient_id, t2.transaction_id, t2.date_created, t2.id\n", "  FROM silver.curated_prx_patient_status t2\n", "  LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "  LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "  WHERE s3.status = 'Active' AND s4.sub_status IN ('SHIPMENT', 'MATERIALS')\n", ") \n", "WHERE is_processed = true \n", "  AND (date_shipped IS NULL OR trim(date_shipped) = '')\n", "\n", "UNION ALL\n", "\n", "-- Validation: Genetic_Test_Result required when Genetic_Test_Completed = 'Y'\n", "SELECT \n", "  'Genetic_Test_Results cannot be null when Genetic_Test_Completed = Y',\n", "  'Genetic_Test_Result',\n", "  cast(genetic_test_result as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND genetic_test_completed = 'Y'\n", "  AND (genetic_test_result IS NULL OR trim(genetic_test_result) = '')\n", "\n", "UNION ALL\n", "\n", "-- Validation: patient_age_on_rx required when rx_date is present\n", "SELECT \n", "  'patient_age_on_rx must be present when Rx Date is provided',\n", "  'PATIENT_AGE_ON_RX',\n", "  cast(patient_age_on_rx as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND rx_date IS NOT NULL\n", "  AND (patient_age_on_rx IS NULL OR cast(patient_age_on_rx as double) IS NULL)\n", "\n", "UNION ALL\n", "\n", "-- Validation: prescriber_npi valid format\n", "SELECT \n", "  'prescriber_npi must be present in valid formate',\n", "  'prescriber_npi',\n", "  cast(concat_ws(', ', prescriber_npi, sp_patient_id) as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    length(prescriber_npi) <> 10 \n", "    OR cast(prescriber_npi as int) IS NULL\n", "    OR prescriber_npi = '**********'\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: prescriber_npi or prescriber_dea must be present\n", "SELECT \n", "  'prescriber_npi must be present and not blank if blank verify prescriber_dea is not blank',\n", "  'prescriber_npi',\n", "  cast(prescriber_npi as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (prescriber_npi IS NULL OR trim(prescriber_npi) = '')\n", "  AND (prescriber_dea IS NULL OR trim(prescriber_dea) = '')\n", "\n", "UNION ALL\n", "\n", "-- Validation: program_consent_date required in format YYYYMMDD when program_consent = 'Y'\n", "SELECT \n", "  'program_consent_date must be in yyyymmmdd format and not blank when program_consent is \"Y\" ',\n", "  'program_consent_date',\n", "  cast(program_consent_date as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND program_consent = 'Y'\n", "  AND (\n", "    program_consent_date IS NULL\n", "    OR trim(program_consent_date) = ''\n", "    OR to_date(program_consent_date, 'yyyyMMdd') IS NULL\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: RX should be unique by rx_date\n", "SELECT \n", "  'Each Rx should have a unique RX Date',\n", "  'rx_date',\n", "  cast(rx_date as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND rx_date_formatted IS NOT NULL\n", "GROUP BY rx_date, date_created, id, rx_number, sp_patient_id, transaction_id\n", "HAVING count(distinct sp_patient_id) > 1\n", "\n", "UNION ALL\n", "\n", "-- Validation: sp_patient_id consistency per patient\n", "SELECT \n", "  'For each patient sp_patient_id should be consistent and unique',\n", "  'sp_patient_id',\n", "  cast(sp_patient_id as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM (\n", "  SELECT distinct sp_patient_id, transaction_id, date_created, id,\n", "         count(distinct concat(trim(sp_patient_id), upper(trim(patient_first_name)), upper(trim(patient_last_name)))) as combination\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true AND hipaa_auth_status = 'Y'\n", "  GROUP BY sp_patient_id, transaction_id, date_created, id\n", "  HAVING combination > 1\n", ")\n", "\n", "UNION ALL\n", "\n", "-- Validation: Duplicate key combinations across sp_patient_id, status, sub_status, etc.\n", "SELECT \n", "  'Duplicate combination found sp_patient_id, status, sub_status, transaction_id, transaction_sequence, transaction_type, status_date_formatted',\n", "  'sp_patient_id, status, sub_status, transaction_id, transaction_sequence',\n", "  concat_ws(' | ', \n", "    cast(a.sp_patient_id as string),\n", "    cast(a.status as string),\n", "    cast(a.sub_status as string),\n", "    cast(a.transaction_id as string),\n", "    cast(a.transaction_sequence as string),\n", "    cast(a.transaction_type as string),\n", "    cast(a.status_date_formatted as string)\n", "  ),\n", "  cast(a.sp_patient_id as string),\n", "  cast(a.transaction_id as string),\n", "  a.date_created,\n", "  a.id\n", "FROM silver.curated_prx_patient_status a\n", "WHERE a.is_processed = true\n", "  AND EXISTS (\n", "    SELECT 1\n", "    FROM silver.curated_prx_patient_status b\n", "    WHERE a.sp_patient_id = b.sp_patient_id\n", "      AND a.status = b.status\n", "      AND a.sub_status = b.sub_status\n", "      AND a.transaction_id = b.transaction_id\n", "      AND a.transaction_sequence = b.transaction_sequence\n", "      AND a.transaction_type = b.transaction_type\n", "      AND a.status_date_formatted = b.status_date_formatted\n", "      AND a.id <> b.id\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: STATUS_DATE should not be in future\n", "SELECT \n", "  'Status Date should never be greater than current date',\n", "  'STATUS_DATE',\n", "  cast(status_date as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND to_timestamp(status_date, 'yyyyMMddHHmmss') > current_date\n", "\n", "UNION ALL\n", "\n", "-- Validation: primary_payor_segment must be from valid values\n", "SELECT \n", "  'primary_payor_segment is not from COMMERCIAL, MEDICAID, MEDICARE, OTHER, PAP, FEDERAL, TRICARE, VA, CASH, UNINSURED, BRIDGE, QUICK START value',\n", "  'primary_payor_segment',\n", "  cast(primary_payor_segment as string),\n", "  cast(t1.sp_patient_id as string),\n", "  cast(t1.transaction_id as string),\n", "  t1.date_created,\n", "  t1.id\n", "FROM silver.curated_prx_patient_status t1\n", "LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id\n", "WHERE \n", "  t1.is_processed = true\n", "  AND upper(s1.status) <> 'PENDING'\n", "  AND (\n", "    primary_payor_segment IS NULL\n", "    OR upper(primary_payor_segment) NOT IN (\n", "      'COMMERCIAL', 'MEDICAID', 'MEDICARE', 'OTHER', 'PAP', 'FEDERAL',\n", "      'TRICARE', 'VA', 'CASH', 'UNINSURED', 'BRIDGE', 'QUICK START'\n", "    )\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: TOTAL_DAILY_DOSE should be present and numeric when status is Active\n", "SELECT \n", "  'TOTAL_DAILY_DOSE contains blank or NULL values or not in Number Format',\n", "  'TOTAL_DAILY_DOSE',\n", "  cast(TOTAL_DAILY_DOSE as string),\n", "  cast(t1.sp_patient_id as string),\n", "  cast(t1.transaction_id as string),\n", "  t1.date_created,\n", "  t1.id\n", "FROM silver.curated_prx_patient_status t1\n", "LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id\n", "WHERE \n", "  t1.is_processed = true\n", "  AND s1.status = 'Active'\n", "  AND (\n", "    TOTAL_DAILY_DOSE IS NULL\n", "    OR trim(TOTAL_DAILY_DOSE) = ''\n", "    OR cast(TOTAL_DAILY_DOSE as double) IS NULL\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: TOTAL_DAILY_DOSE ratio mismatch with quantity_dispensed and days_supply\n", "SELECT \n", "  'Total Daily Dose should not be greater than quantity_dispensed and days_supply for active shipment.',\n", "  'TOTAL_DAILY_DOSE',\n", "  cast(total_daily_dose as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  t2.date_created,\n", "  t2.id\n", "FROM silver.curated_prx_patient_status t2\n", "LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "WHERE t2.is_processed = true\n", "  AND s3.status = 'Active'\n", "  AND s4.sub_status = 'SHIPMENT'\n", "  AND days_supply IS NOT NULL\n", "  AND days_supply <> 0\n", "  AND (total_daily_dose / 100.0) <> (quantity_dispensed / days_supply)\n", "\n", "UNION ALL\n", "\n", "-- Validation: DAYS_SUPPLY null or not numeric for ACTIVE/SHIPMENT\n", "SELECT \n", "  'DAYS_SUPPLY contains blank or NULL values or not in Number format when ACTIVE/SHIPMENT',\n", "  'DAYS_SUPPLY',\n", "  cast(days_supply as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  t2.date_created,\n", "  t2.id\n", "FROM silver.curated_prx_patient_status t2\n", "LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "WHERE t2.is_processed = true\n", "  AND s3.status = 'Active'\n", "  AND s4.sub_status = 'SHIPMENT'\n", "  AND (\n", "    days_supply IS NULL\n", "    OR trim(days_supply) = ''\n", "    OR cast(days_supply as double) IS NULL\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: DAYS_SUPPLY is not numeric (general case)\n", "SELECT \n", "  'DAYS_SUPPLY contains a non numeric format',\n", "  'DAYS_SUPPLY',\n", "  cast(days_supply as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND days_supply IS NOT NULL\n", "  AND trim(days_supply) <> ''\n", "  AND cast(days_supply as double) IS NULL\n", "\n", "UNION ALL\n", "\n", "-- Validation: RX_DATE must be non-blank and in YYYYMMDD format when status is Active\n", "SELECT\n", "  'RX_DATE contains blank or NULL values or not in YYYYMMDD format',\n", "  'RX_DATE',\n", "  cast(t1.rx_date as string),\n", "  cast(t1.sp_patient_id as string),\n", "  cast(t1.transaction_id as string),\n", "  t1.date_created,\n", "  t1.id\n", "FROM silver.curated_prx_patient_status t1\n", "LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id\n", "LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id\n", "WHERE \n", "  t1.is_processed = true\n", "  AND s1.status = 'Active'\n", "  AND (\n", "    rx_date IS NULL\n", "    OR trim(rx_date) = ''\n", "    OR to_date(rx_date, 'yyyyMMdd') IS NULL\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: HIPAA_AUTH_DATE must follow rules based on HIPAA_AUTH_STATUS\n", "SELECT \n", "  'HIPAA_AUTH_DATE must be in YYYYMMDD format and not blank when HIPAA_AUTH_STATUS = Y; must be null when HIPAA_AUTH_STATUS = N',\n", "  'HIPAA_AUTH_DATE',\n", "  cast(hipaa_auth_date as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE \n", "  (\n", "    hipaa_auth_status = 'Y'\n", "    AND (\n", "      hipaa_auth_date IS NULL\n", "      OR trim(hipaa_auth_date) = ''\n", "      OR to_date(hipaa_auth_date, 'yyyyMMdd') IS NULL\n", "      OR length(hipaa_auth_date) <> 8\n", "    )\n", "  )\n", "  OR (\n", "    hipaa_auth_status = 'N'\n", "    AND hipaa_auth_date IS NOT NULL\n", "    AND trim(hipaa_auth_date) <> ''\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- HIPAA_AUTH_STATUS: Null, blank, or invalid format\n", "SELECT\n", "  'HIPAA_AUTH_STATUS contains blank or NULL values or not in Y, N, U format',\n", "  'HIPAA_AUTH_STATUS',\n", "  cast(hipaa_auth_status as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    hipaa_auth_status IS NULL \n", "    OR trim(hipaa_auth_status) = '' \n", "    OR hipaa_auth_status NOT IN ('N', 'Y', 'U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- LATEST_INDICATION: Null, blank, or not in expected values\n", "SELECT \n", "  'LATEST_INDICATION contains blank or NULL values or not in WHIM format',\n", "  'LATEST_INDICATION',\n", "  cast(latest_indication as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    latest_indication IS NULL \n", "    OR trim(latest_indication) = '' \n", "    OR latest_indication NOT IN ('WHIM', 'Other')\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- MARKETING_CONSENT_STATUS: Null, blank, or invalid\n", "SELECT \n", "  'MARKETING_CONSENT_STATUS contains blank or NULL values or not in Y, N, U format',\n", "  'MARKETING_CONSENT_STATUS',\n", "  cast(marketing_consent_status as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    marketing_consent_status IS NULL \n", "    OR trim(marketing_consent_status) = '' \n", "    OR marketing_consent_status NOT IN ('N', 'Y', 'U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- PATIENT_AGE: Null, blank, not integer, or <= 0\n", "SELECT \n", "  'PATIENT_AGE contains blank or NULL values, non-integer values, or values not greater than 0',\n", "  'PATIENT_AGE',\n", "  cast(patient_age as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    patient_age IS NULL\n", "    OR trim(patient_age) = ''\n", "    OR cast(patient_age as int) IS NULL\n", "    OR cast(patient_age as int) <= 0\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- PRESCRIBER_DEA: Invalid character format\n", "SELECT \n", "  'value not in correct varchar format',\n", "  'PRESCRIBER_DEA',\n", "  cast(prescriber_dea as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND prescriber_dea IS NOT NULL\n", "  AND NOT regexp_like(prescriber_dea, '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "-- PRESCRIBER_DEA should be present if NPI is missing\n", "SELECT \n", "  'prescriber_dea should be present when npi is not available',\n", "  'PRESCRIBER_DEA',\n", "  cast(prescriber_dea as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND prescriber_dea IS NULL\n", "  AND prescriber_npi IS NULL\n", "\n", "UNION ALL\n", "-- PRIMARY_COVERAGE_TYPE validation\n", "SELECT \n", "  'PRIMARY_COVERAGE_TYPE contains blank or NULL values or not in M,P,A,C format',\n", "  'PRIMARY_COVERAGE_TYPE',\n", "  cast(primary_coverage_type as string),\n", "  cast(t1.sp_patient_id as string),\n", "  cast(t1.transaction_id as string),\n", "  t1.date_created,\n", "  t1.id\n", "FROM silver.curated_prx_patient_status t1\n", "LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id\n", "LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id\n", "WHERE \n", "  t1.is_processed = true\n", "  AND s1.status <> 'Pending'\n", "  AND (\n", "    primary_coverage_type IS NULL\n", "    OR trim(primary_coverage_type) = ''\n", "    OR primary_coverage_type NOT IN ('M', 'P', 'A', 'C')\n", "    OR length(primary_coverage_type) != 1\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- QUANTITY_DISPENSED must be a valid number\n", "SELECT \n", "  'value not in valid Format of Number',\n", "  'QUANTITY_DISPENSED',\n", "  cast(quantity_dispensed as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND quantity_dispensed IS NOT NULL\n", "  AND cast(quantity_dispensed as int) IS NULL\n", "\n", "UNION ALL\n", "\n", "-- QUANTITY_DISPENSED required when ACTIVE/SHIPMENT\n", "SELECT \n", "  'QUANTITY_DISPENSED contains blank or NULL values when status/substatus combination is ACTIVE/SHIPMENT',\n", "  'QUANTITY_DISPENSED',\n", "  cast(quantity_dispensed as string),\n", "  cast(t2.sp_patient_id as string),\n", "  cast(t2.transaction_id as string),\n", "  t2.date_created,\n", "  t2.id\n", "FROM silver.curated_prx_patient_status t2\n", "LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "WHERE \n", "  t2.is_processed = true\n", "  AND s3.status = 'Active'\n", "  AND s4.sub_status = 'SHIPMENT'\n", "  AND (\n", "    quantity_dispensed IS NULL\n", "    OR trim(quantity_dispensed) = ''\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- TRANSACTION_ID: Null, non-integer, or duplicate with TRANSACTION_SEQUENCE\n", "SELECT \n", "  'TRANSACTION_ID contains blank/NULL, non-integer value, or duplicate combination with Transaction_Sequence',\n", "  'TRANSACTION_ID',\n", "  cast(transaction_id as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM (\n", "  SELECT \n", "    transaction_id,\n", "    transaction_sequence,\n", "    sp_patient_id,\n", "    date_created,\n", "    id,\n", "    COUNT(*) OVER (PARTITION BY transaction_id, transaction_sequence) AS txn_count\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true\n", ") tmp\n", "WHERE \n", "  transaction_id IS NULL\n", "  OR trim(transaction_id) = ''\n", "  OR cast(transaction_id as int) IS NULL\n", "  OR txn_count > 1\n", "\n", "UNION ALL\n", "\n", "-- TRANSACTION_SEQUENCE: blank, non-integer, or incorrect increment\n", "SELECT \n", "  'TRANSACTION_SEQUENCE contains blank, non-integer, or not sequentially incremented values as required',\n", "  'TRANSACTION_SEQUENCE',\n", "  cast(transaction_sequence as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM (\n", "  SELECT \n", "    *,\n", "    row_number() OVER (PARTITION BY transaction_id ORDER BY date_created) - 1 AS expected_seq,\n", "    cast(transaction_sequence as int) AS ts_int\n", "  FROM silver.curated_prx_patient_status\n", "  WHERE is_processed = true\n", ") tmp\n", "WHERE \n", "  transaction_sequence IS NULL\n", "  OR trim(transaction_sequence) = ''\n", "  OR ts_int IS NULL\n", "  OR ts_int <> expected_seq\n", "\n", "UNION ALL\n", "\n", "-- Validation: First record must have status 'Pending' and sub_status 'NEW'\n", "SELECT \n", "  'First record should have status Pending and sub_staus as New',\n", "  'status, sub_status',\n", "  cast(concat(status, sub_status) as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM (\n", "  SELECT \n", "    sp_patient_id,\n", "    t1.date_created,\n", "    t1.id,\n", "    s1.status,\n", "    s2.sub_status,\n", "    t1.status_date,\n", "    transaction_id,\n", "    row_number() OVER (PARTITION BY t1.sp_patient_id ORDER BY t1.status_date) AS rn\n", "  FROM silver.curated_prx_patient_status t1\n", "  LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id\n", "  LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id\n", "  WHERE t1.is_processed = true\n", ") first_record\n", "WHERE rn = 1 \n", "  AND (\n", "    status <> 'Pending' \n", "    OR sub_status <> 'NEW'\n", "  )\n", "\n", "UNION ALL\n", "\n", "-- Validation: TRANSACTION_TYPE should be one of allowed values\n", "SELECT \n", "  'TRANSACTION_TYPE contains blank or NULL values or not in COM, QUICK START, PAP, BRIDGE',\n", "  'TRANSACTION_TYPE',\n", "  cast(transaction_type as string),\n", "  cast(sp_patient_id as string),\n", "  cast(transaction_id as string),\n", "  date_created,\n", "  id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND (\n", "    transaction_type IS NULL\n", "    OR trim(transaction_type) = ''\n", "    OR transaction_type NOT IN ('COM', 'QUICK START', 'PAP', 'BRIDGE')\n", "  )\n", ")\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'PHI field should not be populated when HIPAA_AUTH_STATUS is N' AS error_message,\n", "  'phi_field_violation' AS error_type,\n", "  CASE \n", "    WHEN SHIP_TRACKING_NO IS NOT NULL AND trim(SHIP_TRACKING_NO) != '' THEN concat('SHIP_TRACKING_NO: ', cast(SHIP_TRACKING_NO as string))\n", "    WHEN PATIENT_PHONE IS NOT NULL AND trim(PATIENT_PHONE) != '' THEN concat('PATIENT_PHONE: ', cast(PATIENT_PHONE as string))\n", "    WHEN PATIENT_LAST_NAME IS NOT NULL AND trim(PATIENT_LAST_NAME) != '' THEN concat('PATIENT_LAST_NAME: ', cast(PATIENT_LAST_NAME as string))\n", "    WHEN PATIENT_FIRST_NAME IS NOT NULL AND trim(PATIENT_FIRST_NAME) != '' THEN concat('PATIENT_FIRST_NAME: ', cast(PATIENT_FIRST_NAME as string))\n", "    WHEN PATIENT_EMAIL IS NOT NULL AND trim(PATIENT_EMAIL) != '' THEN concat('PATIENT_EMAIL: ', cast(PATIENT_EMAIL as string))\n", "    WHEN PATIENT_CITY IS NOT NULL AND trim(PATIENT_CITY) != '' THEN concat('PATIENT_CITY: ', cast(PATIENT_CITY as string))\n", "    WHEN PATIENT_ALT_PHONE IS NOT NULL AND trim(PATIENT_ALT_PHONE) != '' THEN concat('PATIENT_ALT_PHONE: ', cast(PATIENT_ALT_PHONE as string))\n", "    WHEN PATIENT_ADDRESS_LINE2 IS NOT NULL AND trim(PATIENT_ADDRESS_LINE2) != '' THEN concat('PATIENT_ADDRESS_LINE2: ', cast(PATIENT_ADDRESS_LINE2 as string))\n", "    WHEN PATIENT_ADDRESS_LINE1 IS NOT NULL AND trim(PATIENT_ADDRESS_LINE1) != '' THEN concat('PATIENT_ADDRESS_LINE1: ', cast(PATIENT_ADDRESS_LINE1 as string))\n", "    WHEN HUB_PATIENT_ID IS NOT NULL AND trim(HUB_PATIENT_ID) != '' THEN concat('HUB_PATIENT_ID: ', cast(HUB_PATIENT_ID as string))\n", "    WHEN COPAY_ASSIST_ID IS NOT NULL AND trim(COPAY_ASSIST_ID) != '' THEN concat('COPAY_ASSIST_ID: ', cast(COPAY_ASSIST_ID as string))\n", "    WHEN CAREGIVER_PHONE IS NOT NULL AND trim(CAREGIVER_PHONE) != '' THEN concat('CAREGIVER_PHONE: ', cast(CAREGIVER_PHONE as string))\n", "    WHEN CAREGIVER_NAME IS NOT NULL AND trim(CAREGIVER_NAME) != '' THEN concat('CAREGIVER_NAME: ', cast(CAREGIVER_NAME as string))\n", "    WHEN CAREGIVER_EMAIL IS NOT NULL AND trim(CAREGIVER_EMAIL) != '' THEN concat('CAREGIVER_EMAIL: ', cast(CAREGIVER_EMAIL as string))\n", "    ELSE 'Unknown column' \n", "  END AS column_value,\n", "  cast(sp_patient_id as string) AS sp_patient_id,\n", "  cast(transaction_id as string) AS transaction_id,\n", "  cast(date_created as string) AS date_created,\n", "  cast(id as string) AS id,\n", "  'pantheRx' AS source,\n", "  'yes' AS is_critical,\n", "  current_timestamp() AS error_timestamp\n", "FROM silver.curated_prx_patient_status\n", "WHERE hipaa_auth_status = 'N'\n", "  AND (\n", "    trim(SHIP_TRACKING_NO) != '' \n", "    OR trim(PATIENT_PHONE) != ''\n", "    OR trim(PATIENT_LAST_NAME) != ''\n", "    OR trim(PATIENT_FIRST_NAME) != ''\n", "    OR trim(PATIENT_EMAIL) != ''\n", "    OR trim(PATIENT_CITY) != ''\n", "    OR trim(PATIENT_ALT_PHONE) != ''\n", "    OR trim(PATIENT_ADDRESS_LINE2) != ''\n", "    OR trim(PATIENT_ADDRESS_LINE1) != ''\n", "    OR trim(HUB_PATIENT_ID) != ''\n", "    OR trim(COPAY_ASSIST_ID) != ''\n", "    OR trim(CAREGIVER_PHONE) != ''\n", "    OR trim(CAREGIVER_NAME) != ''\n", "    OR trim(CAREGIVER_EMAIL) != ''\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  error_message,\n", "  concat(error_column, ' Validation'),\n", "  column_value, \n", "  sp_id as sp_patient_id, \n", "  t_id as transaction_id,\n", "  date_id as DATE_CREATED,\n", "  prxid as ID,\n", "  'pantheRx', \n", "  'no', \n", "  current_timestamp()\n", "FROM (   \n", "\n", "  -- CTE: flattened PRODUCT_LOT\n", "  WITH flattened_product_lots AS (\n", "    SELECT \n", "      t2.product_lot,\n", "      lot_value,\n", "      s3.status,\n", "      s4.sub_status,\n", "      t2.sp_patient_id,\n", "      t2.transaction_id,\n", "      t2.date_created,\n", "      t2.id\n", "    FROM silver.curated_prx_patient_status t2\n", "    LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "    LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "    LATERAL VIEW explode(split(t2.product_lot, ';')) AS lot_value\n", "    WHERE t2.is_processed = true\n", "      AND s3.status = 'Active'\n", "      AND s4.sub_status = 'SHIPMENT'\n", "  ),\n", "\n", "  -- CTE: flattened PRODUCT_EXP_DATE\n", "  flattened_product_exp_date AS (\n", "    SELECT \n", "      t2.product_exp_date,\n", "      lot_value,\n", "      s3.status,\n", "      s4.sub_status,\n", "      t2.sp_patient_id,\n", "      t2.transaction_id,\n", "      t2.date_created,\n", "      t2.id\n", "    FROM silver.curated_prx_patient_status t2\n", "    LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "    LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "    LATERAL VIEW explode(split(t2.product_exp_date, ';')) AS lot_value\n", "    WHERE t2.is_processed = true\n", "      AND s3.status = 'Active'\n", "      AND s4.sub_status = 'SHIPMENT'\n", "  )\n", "\n", "  -- Validation 1: PRODUCT_LOT - non-blank and valid format\n", "  SELECT \n", "    'PRODUCT_LOT must be available in valid Format and not blank when the status/substatus combination is ACTIVE/SHIPMENT' AS error_message,\n", "    'PRODUCT_LOT' AS error_column,\n", "    cast(product_lot as string) AS column_value,\n", "    cast(sp_patient_id as string) AS sp_id,\n", "    cast(transaction_id as string) AS t_id,\n", "    date_created AS date_id,\n", "    id AS prxid\n", "  FROM flattened_product_lots\n", "  WHERE \n", "    product_lot IS NULL \n", "    OR trim(product_lot) = '' \n", "    OR (lot_value IS NOT NULL AND NOT regexp_like(lot_value, '^[a-zA-Z0-9]+$'))\n", "\n", "  UNION ALL\n", "\n", "  -- Validation 2: PRODUCT_EXP_DATE - non-blank and correct date format\n", "  SELECT \n", "    'product_exp_date must be available in valid Format and not blank when the status/substatus combination is ACTIVE/SHIPMENT' AS error_message,\n", "    'product_exp_date' AS error_column,\n", "    cast(product_exp_date as string) AS column_value,\n", "    cast(sp_patient_id as string) AS sp_id,\n", "    cast(transaction_id as string) AS t_id,\n", "    date_created AS date_id,\n", "    id AS prxid\n", "  FROM flattened_product_exp_date\n", "  WHERE \n", "    product_exp_date IS NULL \n", "    OR trim(product_exp_date) = ''\n", "    OR length(product_exp_date) <> 8 \n", "    OR to_date(product_exp_date, 'yyyyMMdd') IS NULL\n", "\n", "  UNION ALL\n", "\n", "  -- Validation 3: PRODUCT_LOT blank check (non-split)\n", "  SELECT \n", "    'PRODUCT_LOT contains blank or NULL values when status/substatus combination is ACTIVE/SHIPMENT',\n", "    'PRODUCT_LOT',\n", "    cast(product_lot as string),\n", "    cast(t2.sp_patient_id as string),\n", "    cast(t2.transaction_id as string),\n", "    t2.date_created,\n", "    t2.id\n", "  FROM silver.curated_prx_patient_status t2\n", "  LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "  LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "  WHERE \n", "    t2.is_processed = true \n", "    AND s3.status = 'Active' \n", "    AND s4.sub_status = 'SHIPMENT' \n", "    AND (product_lot IS NULL OR trim(product_lot) = '')\n", "\n", "  UNION ALL\n", "\n", "  -- Validation 4: PRODUCT_EXP_DATE blank check (non-split)\n", "  SELECT \n", "    'product_exp_date contains blank or NULL values when status/substatus combination is ACTIVE/SHIPMENT',\n", "    'product_exp_date',\n", "    cast(product_exp_date as string),\n", "    cast(t2.sp_patient_id as string),\n", "    cast(t2.transaction_id as string),\n", "    t2.date_created,\n", "    t2.id\n", "  FROM silver.curated_prx_patient_status t2\n", "  LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "  LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "  WHERE \n", "    t2.is_processed = true \n", "    AND s3.status = 'Active' \n", "    AND s4.sub_status = 'SHIPMENT' \n", "    AND (product_exp_date IS NULL OR trim(product_exp_date) = '')\n", "\n", "  UNION ALL\n", "\n", "  SELECT 'CAREGIVER_OK_FOR_MESSAGE contains blank or NULL values or not in Y, N, U format',\n", "       'CAREGIVER_OK_FOR_MESSAGE',\n", "       cast(CAREGIVER_OK_FOR_MESSAGE as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created as date_id,\n", "       id as prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    CAREGIVER_OK_FOR_MESSAGE IS NULL OR trim(CAREGIVER_OK_FOR_MESSAGE) = ''\n", "    OR CAREGIVER_OK_FOR_MESSAGE NOT IN ('N','Y','U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'CLAIM_TYPE contains blank or NULL values or not in M,P,A,C format',\n", "       'CLAIM_TYPE',\n", "       cast(CLAIM_TYPE as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    CLAIM_TYPE IS NULL OR trim(CLAIM_TYPE) = ''\n", "    OR CLAIM_TYPE NOT IN ('M', 'P', 'A', 'C')\n", "    OR length(CLAIM_TYPE) != 1\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'DAW contains blank or NULL values or not in Number format',\n", "       'DAW',\n", "       cast(<PERSON><PERSON><PERSON> as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    DAW IS NULL OR trim(DAW) = ''\n", "    OR try_cast(DAW as double) IS NULL\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'FAMILY_HISTORY_OF_WHIM contains blank or NULL values or not in Y, N, U format',\n", "       'FAMILY_HISTORY_OF_WHIM',\n", "       cast(FAMILY_HISTORY_OF_WHIM as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    FAMILY_HISTORY_OF_WHIM IS NULL OR trim(FAMILY_HISTORY_OF_WHIM) = ''\n", "    OR FAMILY_HISTORY_OF_WHIM NOT IN ('N','Y','U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PATIENT_EAP contains blank or NULL values or not in Y, N, U format',\n", "       'PATIENT_EAP',\n", "       cast(PATIENT_EAP as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    PATIENT_EAP IS NULL OR trim(PATIENT_EAP) = ''\n", "    OR PATIENT_EAP NOT IN ('N','Y','U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PATIENT_GENDER contains blank or NULL values or not in M, F,Unknown/Other format',\n", "       'PATIENT_GENDER',\n", "       cast(PATIENT_GENDER as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    PATIENT_GENDER IS NULL OR trim(PATIENT_GENDER) = ''\n", "    OR PATIENT_GENDER NOT IN ('M', 'F', 'U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PATIENT_OK_FOR_MESSAGE contains blank or NULL values or not in Y, N, U format',\n", "       'PATIENT_OK_FOR_MESSAGE',\n", "       cast(PATIENT_OK_FOR_MESSAGE as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    PATIENT_OK_FOR_MESSAGE IS NULL OR trim(PATIENT_OK_FOR_MESSAGE) = ''\n", "    OR PATIENT_OK_FOR_MESSAGE NOT IN ('Y', 'N', 'U')\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PATIENT_STATE contains blank or NULL values',\n", "       'PATIENT_STATE',\n", "       cast(PATIENT_STATE as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    PATIENT_STATE IS NULL OR trim(PATIENT_STATE) = ''\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PHARMACY_CODE contains blank or NULL values',\n", "       'PHARMACY_CODE',\n", "       cast(PHARMACY_CODE as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    PHARMACY_CODE IS NULL OR trim(PHARMACY_CODE) = ''\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PHARMACY_NPI contains blank or NULL values or not in valid format',\n", "       'PHARMACY_NPI',\n", "       cast(PHARMACY_NPI as string),\n", "       cast(sp_patient_id as string),\n", "       cast(transaction_id as string),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    PHARMACY_NPI IS NULL OR trim(PHARMACY_NPI) = ''\n", "    OR length(PHARMACY_NPI) <> 10\n", "    OR try_cast(PHARMACY_NPI as int) IS NULL\n", "    OR PHARMACY_NPI = '**********'\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRESCRIBER_ADDRESS_LINE1 contains blank or NULL values',\n", "       'PRESCRIBER_ADDRESS_LINE1',\n", "       CAST(PRESCRIBER_ADDRESS_LINE1 AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created AS date_id,\n", "       id AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRESCRIBER_ADDRESS_LINE1 IS NULL OR TRIM(PRESCRIBER_ADDRESS_LINE1) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRESCRIBER_CITY contains blank or NULL values',\n", "       'PRESCRIBER_CITY',\n", "       CAST(PRESCRIBER_CITY AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRESCRIBER_CITY IS NULL OR TRIM(PRESCRIBER_CITY) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRESCRIBER_FIRST_NAME contains blank or NULL values',\n", "       'PRESCRIBER_FIRST_NAME',\n", "       CAST(PRESCRIBER_FIRST_NAME AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRESCRIBER_FIRST_NAME IS NULL OR TRIM(PRESCRIBER_FIRST_NAME) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRESCRIBER_LAST_NAME contains blank or NULL values',\n", "       'PRESCRIBER_LAST_NAME',\n", "       CAST(PRESCRIBER_LAST_NAME AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRESCRIBER_LAST_NAME IS NULL OR TRIM(PRESCRIBER_LAST_NAME) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRESCRIBER_PHONE contains blank or NULL values or not in valid format',\n", "       'PRESCRIBER_PHONE',\n", "       CAST(PRESCRIBER_PHONE AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND (\n", "    PRESCRIBER_PHONE IS NULL OR TRIM(PRESCRIBER_PHONE) = ''\n", "    OR LENGTH(PRESCRIBER_PHONE) <> 10\n", "    OR TRY_CAST(PRESCRIBER_PHONE AS INT) IS NULL\n", "    OR PRESCRIBER_PHONE = '**********'\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRESCRIBER_SPECIALTY contains blank or NULL values',\n", "       'PRESCRIBER_SPECIALTY',\n", "       CAST(PRESCRIBER_SPECIALTY AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRESCRIBER_SPECIALTY IS NULL OR TRIM(PRESCRIBER_SPECIALTY) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRESCRIBER_STATE contains blank or NULL values',\n", "       'PRESCRIBER_STATE',\n", "       CAST(PRESCRIBER_STATE AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRESCRIBER_STATE IS NULL OR TRIM(PRESCRIBER_STATE) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRIMARY_PA_REQUIRED contains blank or NULL values or not in Y, N, U format',\n", "       'PRIMARY_PA_REQUIRED',\n", "       CAST(PRIMARY_PA_REQUIRED AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (\n", "  PRIMARY_PA_REQUIRED IS NULL OR TRIM(PRIMARY_PA_REQUIRED) = ''\n", "  OR PRIMARY_PA_REQUIRED NOT IN ('Y', 'N', 'U')\n", ")\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRIMARY_PBM_BIN contains blank or NULL values or not in number format',\n", "       'PRIMARY_PBM_BIN',\n", "       CAST(PRIMARY_PBM_BIN AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (\n", "  PRIMARY_PBM_BIN IS NULL OR TRIM(PRIMARY_PBM_BIN) = ''\n", "  OR LENGTH(PRIMARY_PBM_BIN) <> 6\n", "  OR TRY_CAST(PRIMARY_PBM_BIN AS INT) IS NULL\n", ")\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRIMARY_PBM_NAME contains blank or NULL values',\n", "       'PRIMARY_PBM_NAME',\n", "       CAST(PRIMARY_PBM_NAME AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRIMARY_PBM_NAME IS NULL OR TRIM(PRIMARY_PBM_NAME) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRIMARY_PBM_NAME should present when the primary coverage type is \"Pharmaceutical\"',\n", "       'PRIMARY_PBM_NAME',\n", "       CAST(PRIMARY_PBM_NAME AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       date_created,\n", "       id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND PRIMARY_COVERAGE_TYPE = 'p'\n", "  AND (PRIMARY_PBM_NAME IS NULL OR TRIM(PRIMARY_PBM_NAME) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRIMARY_PBM_PLAN_ID contains blank or NULL values','PRIMARY_PBM_PLAN_ID', CAST(PRIMARY_PBM_PLAN_ID AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRIMARY_PBM_PLAN_ID IS NULL OR TRIM(PRIMARY_PBM_PLAN_ID) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PRODUCT_NAME contains blank or NULL values','PRODUCT_NAME', CAST(PRODUCT_NAME AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PRODUCT_NAME IS NULL OR TRIM(PRODUCT_NAME) = '')\n", "\n", "UNION ALL\n", "\n", "SELECT 'PROGRAM_NAME contains blank or NULL values','PROGRAM_NAME', CAST(PROGRAM_NAME AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (PROGRAM_NAME IS NULL OR TRIM(PROGRAM_NAME) = '' OR PROGRAM_NAME NOT IN ('Xolremdi'))\n", "\n", "UNION ALL\n", "\n", "SELECT 'REC_DATE contains blank or NULL values or not in YYYYMMDDHHMMSS','REC_DATE', CAST(REC_DATE AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (REC_DATE IS NULL OR TRIM(REC_DATE) = '' OR LENGTH(REC_DATE) <> 14 OR TO_TIMESTAMP(REC_DATE, 'yyyyMMddHHmmss') IS NULL)\n", "\n", "UNION ALL\n", "\n", "SELECT 'REFERRAL_SOURCE contains blank or NULL values or not in HUB,DIRECT,PHARM','REFERRAL_SOURCE', CAST(REFERRAL_SOURCE AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (REFERRAL_SOURCE IS NULL OR TRIM(REFERRAL_SOURCE) = '' OR REFERRAL_SOURCE NOT IN ('HUB', 'DIRECT', 'PHARM'))\n", "\n", "UNION ALL\n", "\n", "SELECT 'REF<PERSON><PERSON>_REMAINING contains blank or NULL values or not in Number format','REFILLS_REMAINING', CAST(REFILLS_REMAINING AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (REFIL<PERSON>_REMAINING IS NULL OR TRIM(REFILLS_REMAINING) = '' OR TRY_CAST(REFILLS_REMAINING AS INT) IS NULL)\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'REFILLS_REMAINING should be (Rx Refills Allowed) - (Rx Fill Number + 1) when status/substatus combination is ACTIVE/SHIPMENT',\n", "  'REFILLS_REMAINING',\n", "  CAST(t2.REFILLS_REMAINING AS STRING),\n", "  CAST(t2.sp_patient_id AS STRING),\n", "  CONCAT(\n", "    CAST(t2.transaction_id AS STRING), \n", "    ' | rx_refills_allowed = ', \n", "    CAST(t2.rx_refills_allowed AS STRING), \n", "    ' | rx_fill_number = ', \n", "    CAST(t2.rx_fill_number AS STRING)\n", "  ),\n", "  t2.date_created, \n", "  t2.id\n", "FROM silver.curated_prx_patient_status t2\n", "LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id\n", "LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id\n", "WHERE t2.is_processed = true\n", "  AND s3.status = 'Active'\n", "  AND s4.sub_status = 'SHIPMENT'\n", "  AND t2.REFILLS_REMAINING <> (t2.rx_refills_allowed - (t2.rx_fill_number + 1))\n", "\n", "\n", "UNION ALL\n", "\n", "SELECT 'RX_FILL_NUMBER must be less than or equal to the Refills Allowed and should be a valid integer','RX_FILL_NUMBER', CAST(RX_FILL_NUMBER AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (RX_FILL_NUMBER IS NULL OR TRIM(RX_FILL_NUMBER) = '' OR TRY_CAST(RX_FILL_NUMBER AS INT) IS NULL OR TRY_CAST(RX_FILL_NUMBER AS INT) > rx_refills_allowed)\n", "\n", "UNION ALL\n", "\n", "SELECT 'RX_NUMBER contains blank or NULL values or not in Number format','RX_NUMBER', CAST(RX_NUMBER AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (RX_NUMBER IS NULL OR TRIM(RX_NUMBER) = '' OR TRY_CAST(RX_NUMBER AS INT) IS NULL)\n", "\n", "UNION ALL\n", "\n", "SELECT 'RX_REFILLS_ALLOWED contains blank or NULL values or not in Number format','RX_REFILLS_ALLOWED', CAST(RX_REFILLS_ALLOWED AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (RX_REFILLS_ALLOWED IS NULL OR TRIM(RX_REFILLS_ALLOWED) = '' OR TRY_CAST(RX_REFILLS_ALLOWED AS INT) IS NULL)\n", "\n", "UNION ALL\n", "\n", "SELECT 'SECONDARY_PA_REQUIRED contains blank or NULL values or not in Y,N,U format','SECONDARY_PA_REQUIRED', CAST(SECONDARY_PA_REQUIRED AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (SECONDARY_PA_REQUIRED IS NULL OR TRIM(SECONDARY_PA_REQUIRED) = '' OR SECONDARY_PA_REQUIRED NOT IN ('Y', 'N', 'U'))\n", "\n", "UNION ALL\n", "\n", "SELECT 'TERTIARY_PA_REQUIRED contains blank or NULL values or not in Y,N,U format','TERTIARY_PA_REQUIRED', CAST(TERTIARY_PA_REQUIRED AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (TERTIARY_PA_REQUIRED IS NULL OR TRIM(TERTIARY_PA_REQUIRED) = '' OR TERTIARY_PA_REQUIRED NOT IN ('Y', 'N', 'U'))\n", "\n", "UNION ALL\n", "\n", "SELECT 'TOTAL_DAILY_DOSE contains blank or NULL values or not in Number Format','TOTAL_DAILY_DOSE', CAST(TOTAL_DAILY_DOSE AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true AND (TOTAL_DAILY_DOSE IS NULL OR TRIM(TOTAL_DAILY_DOSE) = '' OR TRY_CAST(TOTAL_DAILY_DOSE AS INT) IS NULL)\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in valid email Format' AS error_message,\n", "       'CONTACT_EMAIL' AS error_column,\n", "       CAST(CONTACT_EMAIL AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED ,\n", "       ID \n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND NOT REGEXP_LIKE(CONTACT_EMAIL, '^[a-zA-Z0-9._#%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in valid Format' AS error_message,\n", "       'CONTACT_NAME' AS error_column,\n", "       CAST(CONTACT_NAME AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND CONTACT_NAME IS NOT NULL \n", "  AND NOT REGEXP_LIKE(REPLACE(CONTACT_NAME, ' ', ''), '^[a-zA-Z\\\\s''\\\\-\\\\.]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in valid Format' AS error_message,\n", "       'DIAG_CODE_2' AS error_column,\n", "       CAST(DIAG_CODE_2 AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND NOT REGEXP_LIKE(TRIM(DIAG_CODE_2), '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'MARKETING_CONSENT_DATE must be in yyyymmmdd format and not blank when MARKETING_CONSENT_STATUS is \"Y\"' AS error_message,\n", "       'MARKETING_CONSENT_DATE' AS error_column,\n", "       CAST(MARKETING_CONSENT_DATE AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND MARKETING_CONSENT_STATUS = 'Y'\n", "  AND (\n", "        MARKETING_CONSENT_DATE IS NULL \n", "     OR TRIM(MARKETING_CONSENT_DATE) = '' \n", "     OR TRY_CAST(MARKETING_CONSENT_DATE AS DATE) IS NULL\n", "     OR LENGTH(MARKETING_CONSENT_DATE) <> 8\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in valid Format as 10 digit' AS error_message,\n", "       'PRESCRIBER_FAX' AS error_column,\n", "       CAST(PRESCRIBER_FAX AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND (\n", "        LENGTH(PRESCRIBER_FAX) <> 10 \n", "     OR TRY_CAST(PRESCRIBER_FAX AS INT) IS NULL\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in valid yyyymmdd Format' AS error_message,\n", "       'PRIMARY_PA_AUTH_END' AS error_column,\n", "       CAST(PRIMARY_PA_AUTH_END AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND PRIMARY_PA_AUTH_END IS NOT NULL \n", "  AND (\n", "        TRY_CAST(PRIMARY_PA_AUTH_END AS DATE) IS NULL \n", "     OR LENGTH(PRIMARY_PA_AUTH_END) <> 8\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in valid yyyymmdd Format or may be greater than current date' AS error_message,\n", "       'PRIMARY_PA_AUTH_START' AS error_column,\n", "       CAST(PRIMARY_PA_AUTH_START AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED,\n", "       ID \n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND PRIMARY_PA_AUTH_START IS NOT NULL \n", "  AND (\n", "        TRY_CAST(PRIMARY_PA_AUTH_START AS DATE) IS NULL \n", "     OR LENGTH(PRIMARY_PA_AUTH_START) <> 8\n", "     OR TRY_CAST(PRIMARY_PA_AUTH_START AS DATE) > CURRENT_DATE()\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in valid format as single char in M,P,A,C format' AS error_message,\n", "       'SECONDARY_COVERAGE_TYPE' AS error_column,\n", "       CAST(SECONDARY_COVERAGE_TYPE AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_COVERAGE_TYPE IS NOT NULL\n", "  AND (\n", "    SECONDARY_COVERAGE_TYPE NOT IN ('M', 'P', 'A', 'C') OR LENGTH(SECONDARY_COVERAGE_TYPE) <> 1\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct YYYYMMDD format' AS error_message,\n", "       'SECONDARY_PA_AUTH_END' AS error_column,\n", "       CAST(SECONDARY_PA_AUTH_END AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_PA_AUTH_END IS NOT NULL\n", "  AND (\n", "    TO_DATE(SECONDARY_PA_AUTH_END, 'yyyyMMdd') IS NULL\n", "    OR LENGTH(SECONDARY_PA_AUTH_END) <> 8\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct YYYYMMDD format or may be greater than current date' AS error_message,\n", "       'SECONDARY_PA_AUTH_START' AS error_column,\n", "       CAST(SECONDARY_PA_AUTH_START AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_PA_AUTH_START IS NOT NULL\n", "  AND (\n", "    TO_DATE(SECONDARY_PA_AUTH_START, 'yyyyMMdd') IS NULL\n", "    OR LENGTH(SECONDARY_PA_AUTH_START) <> 8\n", "    OR TO_DATE(SECONDARY_PA_AUTH_START, 'yyyyMMdd') > CURRENT_DATE()\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not from Commercial, Medicaid, Medicare, Other, PAP, Federal, Tricare, VA, Cash, Uninsured, Copay Assistance' AS error_message,\n", "       'SECONDARY_PAYOR_TYPE' AS error_column,\n", "       CAST(SECONDARY_PAYOR_TYPE AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_PAYOR_TYPE NOT IN (\n", "    'Commercial','Medicaid','Medicare','Other','PAP','Federal','Tricare','VA','Cash','Uninsured','Copay Assistance'\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct 6 digit format' AS error_message,\n", "       'SECONDARY_PBM_BIN' AS error_column,\n", "       CAST(SECONDARY_PBM_BIN AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND NOT REGEXP_LIKE(SECONDARY_PBM_BIN, '^[0-9]{6}$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct varchar format' AS error_message,\n", "       'SECONDARY_PBM_GROUP' AS error_column,\n", "       CAST(SECONDARY_PBM_GROUP AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_PBM_GROUP IS NOT NULL\n", "  AND NOT REGEXP_LIKE(SECONDARY_PBM_GROUP, '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct varchar format' AS error_message,\n", "       'SECONDARY_PBM_NAME' AS error_column,\n", "       CAST(SECONDARY_PBM_NAME AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_PBM_NAME IS NOT NULL\n", "  AND NOT REGEXP_LIKE(REPLACE(SECONDARY_PBM_NAME, ' ', ''), '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct varchar format' AS error_message,\n", "       'SECONDARY_PBM_PCN' AS error_column,\n", "       CAST(SECONDARY_PBM_PCN AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_PBM_PCN IS NOT NULL\n", "  AND NOT REGEXP_LIKE(SECONDARY_PBM_PCN, '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct varchar format' AS error_message,\n", "       'SECONDARY_PBM_PLAN_ID' AS error_column,\n", "       CAST(SECONDARY_PBM_PLAN_ID AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SECONDARY_PBM_PLAN_ID IS NOT NULL\n", "  AND NOT REGEXP_LIKE(REPLACE(SECONDARY_PBM_PLAN_ID, ' ', ''), '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct varchar format' AS error_message,\n", "       'SHIP_CARRIER' AS error_column,\n", "       CAST(SHIP_CARRIER AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SHIP_CARRIER IS NOT NULL\n", "  AND NOT REGEXP_LIKE(SHIP_CARRIER, '^[a-zA-Z]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not from given Home, Office, Other values' AS error_message,\n", "       'SHIP_TO_LOCATION' AS error_column,\n", "       CAST(SHIP_TO_LOCATION AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND SHIP_TO_LOCATION IS NOT NULL\n", "  AND SHIP_TO_LOCATION NOT IN ('Home', 'Office', 'Other')\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not from M, P, A, C as single character' AS error_message,\n", "       'TERTIARY_COVERAGE_TYPE' AS error_column,\n", "       CAST(TERTIARY_COVERAGE_TYPE AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND TERTIARY_COVERAGE_TYPE IS NOT NULL\n", "  AND (TERTIARY_COVERAGE_TYPE NOT IN ('M', 'P', 'A', 'C') OR LENGTH(TERTIARY_COVERAGE_TYPE) <> 1)\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct date format' AS error_message,\n", "       'TERTIARY_PA_AUTH_END' AS error_column,\n", "       CAST(TERTIARY_PA_AUTH_END AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND TERTIARY_PA_AUTH_END IS NOT NULL\n", "  AND (\n", "    TO_DATE(TERTIARY_PA_AUTH_END, 'yyyyMMdd') IS NULL OR LENGTH(TERTIARY_PA_AUTH_END) <> 8\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT 'value not in correct date format or may be greater than current date' AS error_message,\n", "       'TERTIARY_PA_AUTH_START' AS error_column,\n", "       CAST(TERTIARY_PA_AUTH_START AS STRING),\n", "       CAST(sp_patient_id AS STRING),\n", "       CAST(transaction_id AS STRING),\n", "       DATE_CREATED AS date_id,\n", "       ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true\n", "  AND TERTIARY_PA_AUTH_START IS NOT NULL\n", "  AND (\n", "    TO_DATE(TERTIARY_PA_AUTH_START, 'yyyyMMdd') IS NULL\n", "    OR LENGTH(TERTIARY_PA_AUTH_START) <> 8\n", "    OR TO_DATE(TERTIARY_PA_AUTH_START, 'yyyyMMdd') > CURRENT_DATE()\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not from Commercial Medicaid,Medicare,Other,PAP,Federal,Tricare,VA,Cash,Uninsured,Copay Assistance',\n", "  'tertiary_payor_type',\n", "  CAST(tertiary_payor_type AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND tertiary_payor_type NOT IN (\n", "    'Commercial','Medicaid','Medicare','Other','PAP','Federal','Tricare','VA','Cash','Uninsured','Copay Assistance'\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct 6 digit format',\n", "  'TERTIARY_PBM_BIN',\n", "  CAST(TERTIARY_PBM_BIN AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND NOT REGEXP_LIKE(TERTIARY_PBM_BIN, '^\\\\d{6}$')\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct varchar format',\n", "  'TERTIARY_PBM_GROUP',\n", "  CAST(TERTIARY_PBM_GROUP AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND TERTIARY_PBM_GROUP IS NOT NULL \n", "  AND NOT REGEXP_LIKE(TERTIARY_PBM_GROUP, '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct varchar format',\n", "  'TERTIARY_PBM_NAME',\n", "  CAST(TERTIARY_PBM_NAME AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND TERTIARY_PBM_NAME IS NOT NULL \n", "  AND NOT REGEXP_LIKE(REPLACE(TERTIARY_PBM_NAME, ' ', ''), '^[a-zA-Z0-9-]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct varchar format',\n", "  'TERTIARY_PBM_PCN',\n", "  CAST(TERTIARY_PBM_PCN AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND TERTIARY_PBM_PCN IS NOT NULL \n", "  AND NOT REGEXP_LIKE(TERTIARY_PBM_PCN, '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct varchar format',\n", "  'TERTIARY_PBM_PLAN_ID',\n", "  CAST(TERTIARY_PBM_PLAN_ID AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND TERTIARY_PBM_PLAN_ID IS NOT NULL \n", "  AND NOT REGEXP_LIKE(REPLACE(TERTIARY_PBM_PLAN_ID, ' ', ''), '^[a-zA-Z0-9-]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct varchar format or UNIT_OF_MEASURE not EA when status = Active',\n", "  'UNIT_OF_MEASURE',\n", "  CAST(t1.UNIT_OF_MEASURE AS STRING),\n", "  CAST(t1.sp_patient_id AS STRING),\n", "  CAST(t1.transaction_id AS STRING),\n", "  t1.DATE_CREATED AS date_id,\n", "  t1.ID AS prxid\n", "FROM silver.curated_prx_patient_status t1\n", "LEFT JOIN dwh_utils.status_catalog s1 \n", "  ON t1.status = s1.id\n", "WHERE t1.is_processed = true\n", "  AND (\n", "    (UNIT_OF_MEASURE IS NOT NULL AND NOT REGEXP_LIKE(UNIT_OF_MEASURE, '^[A-Za-z]+$'))\n", "    OR (s1.status = 'Active' AND UNIT_OF_MEASURE <> 'EA')\n", "  )\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct varchar format',\n", "  'PRIMARY_PBM_GROUP',\n", "  CAST(PRIMARY_PBM_GROUP AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND PRIMARY_PBM_GROUP IS NOT NULL \n", "  AND NOT REGEXP_LIKE(PRIMARY_PBM_GROUP, '^[a-zA-Z0-9-]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'value not in correct varchar format',\n", "  'PRIMARY_PBM_PCN',\n", "  CAST(PRIMARY_PBM_PCN AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND PRIMARY_PBM_PCN IS NOT NULL \n", "  AND NOT REGEXP_LIKE(PRIMARY_PBM_PCN, '^[a-zA-Z0-9]+$')\n", "\n", "UNION ALL\n", "\n", "SELECT \n", "  'Patient Weight should not be 0',\n", "  'PATIENT_WEIGHT_KG',\n", "  CAST(PATIENT_WEIGHT_KG AS STRING),\n", "  CAST(sp_patient_id AS STRING),\n", "  CAST(transaction_id AS STRING),\n", "  DATE_CREATED AS date_id,\n", "  ID AS prxid\n", "FROM silver.curated_prx_patient_status\n", "WHERE is_processed = true \n", "  AND PATIENT_WEIGHT_KG = 0\n", "\n", ")\n", "\n", ";\n", "\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ec0a5cc3-2441-41f1-9c31-aa79ad34e1d8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "select *  FROM silver.prx_dqm_view;"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 5666022865895645, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "dqm.sql", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}