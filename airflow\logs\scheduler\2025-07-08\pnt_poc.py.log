[2025-07-08T09:51:05.039+0000] {processor.py:161} INFO - Started process (PID=189) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:05.041+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:51:05.045+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.044+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:05.093+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.088+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:51:05.096+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:05.131+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.101 seconds
[2025-07-08T09:51:35.744+0000] {processor.py:161} INFO - Started process (PID=229) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:35.746+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:51:35.749+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.748+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:35.789+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.780+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:51:35.792+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:35.826+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.092 seconds
[2025-07-08T09:52:06.030+0000] {processor.py:161} INFO - Started process (PID=245) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:06.033+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:52:06.036+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.035+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:06.087+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.077+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:52:06.091+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:06.137+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.118 seconds
[2025-07-08T09:52:30.044+0000] {processor.py:161} INFO - Started process (PID=261) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:30.047+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:52:30.050+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.049+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:30.095+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.086+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:52:30.100+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:30.139+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.114 seconds
