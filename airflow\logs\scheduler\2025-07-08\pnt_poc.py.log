[2025-07-08T09:51:05.039+0000] {processor.py:161} INFO - Started process (PID=189) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:05.041+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:51:05.045+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.044+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:05.093+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.088+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:51:05.096+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:05.131+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.101 seconds
[2025-07-08T09:51:35.744+0000] {processor.py:161} INFO - Started process (PID=229) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:35.746+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:51:35.749+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.748+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:35.789+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.780+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:51:35.792+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:51:35.826+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.092 seconds
[2025-07-08T09:52:06.030+0000] {processor.py:161} INFO - Started process (PID=245) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:06.033+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:52:06.036+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.035+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:06.087+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.077+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:52:06.091+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:06.137+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.118 seconds
[2025-07-08T09:52:30.044+0000] {processor.py:161} INFO - Started process (PID=261) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:30.047+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:52:30.050+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.049+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:30.095+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.086+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:52:30.100+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:52:30.139+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.114 seconds
[2025-07-08T09:58:22.493+0000] {processor.py:161} INFO - Started process (PID=190) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:58:22.495+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:58:22.498+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:22.498+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:58:22.530+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:22.524+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:58:22.532+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:58:22.600+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.117 seconds
[2025-07-08T09:58:59.863+0000] {processor.py:161} INFO - Started process (PID=230) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:58:59.866+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:58:59.870+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:59.870+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:58:52.243+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:52.231+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:58:52.247+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:58:52.278+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.113 seconds
[2025-07-08T09:59:19.542+0000] {processor.py:161} INFO - Started process (PID=246) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:59:19.546+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:59:19.552+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:19.551+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:59:19.596+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:19.589+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:59:19.599+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:59:19.634+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.106 seconds
[2025-07-08T09:59:49.996+0000] {processor.py:161} INFO - Started process (PID=262) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:59:49.999+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T09:59:50.002+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:50.002+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:59:50.054+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:50.041+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T09:59:50.056+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T09:59:50.156+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.176 seconds
[2025-07-08T10:00:20.659+0000] {processor.py:161} INFO - Started process (PID=278) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:00:20.663+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T10:00:20.667+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:20.667+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:00:20.712+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:20.703+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T10:00:20.715+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:00:20.749+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.098 seconds
[2025-07-08T10:00:43.204+0000] {processor.py:161} INFO - Started process (PID=293) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:00:43.207+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T10:00:43.212+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:43.211+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:00:43.268+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:43.256+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T10:00:43.271+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:00:43.356+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.165 seconds
[2025-07-08T10:01:20.896+0000] {processor.py:161} INFO - Started process (PID=309) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:01:20.898+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T10:01:20.901+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:20.901+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:01:13.257+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:13.250+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T10:01:13.260+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:01:13.291+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.087 seconds
[2025-07-08T10:01:38.327+0000] {processor.py:161} INFO - Started process (PID=325) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:01:38.329+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T10:01:38.332+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:38.331+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:01:38.394+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:38.381+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T10:01:38.399+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:01:38.438+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.121 seconds
[2025-07-08T10:02:10.932+0000] {processor.py:161} INFO - Started process (PID=341) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:02:10.934+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T10:02:10.937+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:10.937+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:02:03.259+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:03.250+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T10:02:03.262+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:02:03.330+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.132 seconds
[2025-07-08T10:02:33.952+0000] {processor.py:161} INFO - Started process (PID=357) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:02:33.955+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T10:02:33.960+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:33.959+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:02:34.027+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:34.016+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T10:02:34.032+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:02:34.067+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.128 seconds
[2025-07-08T10:03:04.707+0000] {processor.py:161} INFO - Started process (PID=373) to work on /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:03:04.711+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/pnt_poc.py for tasks to queue
[2025-07-08T10:03:04.715+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:04.714+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:03:04.750+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:04.745+0000] {dagbag.py:355} ERROR - Failed to import: /opt/airflow/dags/pnt_poc.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/airflow/models/dagbag.py", line 351, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/opt/airflow/dags/pnt_poc.py", line 4, in <module>
    from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
ModuleNotFoundError: No module named 'airflow.providers.databricks'
[2025-07-08T10:03:04.752+0000] {processor.py:842} WARNING - No viable dags retrieved from /opt/airflow/dags/pnt_poc.py
[2025-07-08T10:03:04.788+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/pnt_poc.py took 0.098 seconds
