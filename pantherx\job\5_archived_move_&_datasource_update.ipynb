{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "aae8dfe8-40cb-4874-902d-31c0879fa8b6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import re\n", "\n", "pattern = re.compile(r\"PNT_PRG_Status_[^/]*$\")\n", "source_path = \"/mnt/dev/data/\"\n", "archive_path = \"/mnt/dev/archive/\"\n", "\n", "# dbutils.widgets.text(\"key\", \"456\", \"Enter key\")\n", "# dbutils.widgets.text(\"source_system_id\", \"1\", \"Enter id\")\n", "params = dbutils.widgets.getAll()\n", "source_system_id = spark.sql(\"select id from dwh_utils.source_system_master where systemname = 'pantherx';\").first()['id']\n", "\n", "for file in dbutils.fs.ls(source_path):\n", "    if pattern.search(file.name):\n", "        try:\n", "            dbutils.fs.cp(file.path, f\"{archive_path}{file.name}\")\n", "            dbutils.fs.rm(file.path)\n", "\n", "            datasource_id =spark.sql(f\"select max(id) as id from dwh_utils.data_source_tracker where filename = '{file.name}' and STATUS = 'PROCESSING' and SOURCE_SYSTEM_ID = {source_system_id};\").first()['id']\n", "\n", "            spark.sql(f\"\"\"\n", "                UPDATE dwh_utils.data_source_tracker\n", "                SET STATUS = 'PROCESSED'\n", "                WHERE DATE(date_created) = CURRENT_DATE()\n", "                  AND filename = '{file.name}'\n", "                  AND STATUS = 'PROCESSING'\n", "                  AND SOURCE_SYSTEM_ID = source_system_id\n", "                  AND ID = datasource_id\n", "            \"\"\")\n", "        except:\n", "            spark.sql(f\"\"\"\n", "                UPDATE dwh_utils.data_source_tracker\n", "                SET STATUS = 'ERROR'\n", "                WHERE DATE(date_created) = CURRENT_DATE()\n", "                  AND STATUS = 'PROCESSING'\n", "                  AND SOURCE_SYSTEM_ID = source_system_id\n", "            \"\"\")\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "5_archived_move_&_datasource_update", "widgets": {"key": {"currentValue": "456", "nuid": "905fc4c4-e406-4784-aa6f-792b728d0e78", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "456", "label": "Enter key", "name": "key", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "456", "label": "Enter key", "name": "key", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "source_system_id": {"currentValue": "1", "nuid": "e5b09901-dad7-461e-b7bf-45debe7bd540", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "1", "label": "Enter id", "name": "source_system_id", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "1", "label": "Enter id", "name": "source_system_id", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}