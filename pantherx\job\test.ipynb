{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "9f9176a2-1c0c-4ea3-bee9-74cf34485e81", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "WITH source AS (\n", "  SELECT \n", "    sp_patient_id, \n", "    transaction_id, \n", "    concat_ws(',', collect_list(error_type)) AS error_type\n", "  FROM dwh_dqm.prx_dqm_view\n", "  WHERE DATE(date_created) = CURRENT_DATE() \n", "    AND is_critical = 'yes'\n", "  GROUP BY sp_patient_id, transaction_id\n", "),\n", "non_empty_source AS (\n", "  SELECT * FROM source WHERE sp_patient_id IS NOT NULL OR transaction_id IS NOT NULL\n", ")\n", "\n", "MERGE INTO silver.curated_prx_patient_status AS target\n", "USING non_empty_source AS source\n", "ON target.sp_patient_id <=> source.sp_patient_id\n", "   AND target.transaction_id <=> source.transaction_id\n", "   AND DATE(target.date_created) = CURRENT_DATE()\n", "WHEN MATCHED THEN \n", "  UPDATE SET \n", "    target.IS_PROCESSED = 'FALSE',\n", "    target.PROCESSING_STATUS = 'ERROR',\n", "    target.PROCESSING_ERROR = source.error_type;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "a81acf62-bce2-40bd-9698-44c52396302d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "\n", "WITH latest_sequence AS (\n", "  SELECT TRANSACTION_ID,TRANSACTION_TYPE,SP_PATIENT_ID,MAX(TRANSACTION_SEQUENCE) AS MAX_SEQ\n", "  FROM gold.PRX_PATIENT_STATUS\n", "  WHERE IS_DELETED = 'FALSE'\n", "  GROUP BY TRANSACTION_ID, TRANSACTION_TYPE, SP_PATIENT_ID\n", ")\n", "UPDATE gold.PRX_PATIENT_STATUS AS a\n", "SET IS_DELETED = 'TRUE'\n", "WHERE EXISTS (\n", "  SELECT 1 FROM latest_sequence AS b\n", "  WHERE a.TRANSACTION_ID = b.TRANSACTION_ID\n", "    AND a.TRANSACTION_TYPE = b.TRANSACTION_TYPE\n", "    AND a.SP_PATIENT_ID = b.SP_PATIENT_ID\n", "    AND a.TRANSACTION_SEQUENCE < b.MAX_SEQ\n", ");\n", "\n", "update silver.curated_prx_patient_status set PROCESSING_STATUS = 'COMPLETED'  where IS_PROCESSED = 'TRUE' AND PROCESSING_STATUS = 'PROCESSING' and date(date_created) = current_date();\n", "\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "ed832509-52e3-4ed3-942a-17171e6863ea", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "MERGE INTO silver.curated_prx_patient_status AS target\n", "USING (\n", "  SELECT \n", "    sp_patient_id, \n", "    transaction_id, \n", "    concat_ws(',', collect_list(error_type)) AS error_type\n", "  FROM dwh_dqm.prx_dqm_view\n", "  WHERE DATE(date_created) = CURRENT_DATE() \n", "    AND is_critical = 'yes'\n", "  GROUP BY sp_patient_id, transaction_id\n", ") AS source\n", "ON target.sp_patient_id <=> source.sp_patient_id\n", "   AND target.transaction_id <=> source.transaction_id\n", "   AND DATE(target.date_created) = CURRENT_DATE()\n", "WHEN MATCHED AND source.error_type IS NOT NULL THEN \n", "  UPDATE SET \n", "    target.IS_PROCESSED = 'FALSE',\n", "    target.PROCESSING_STATUS = 'ERROR',\n", "    target.PROCESSING_ERROR = source.error_type;\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c15f16b6-39fa-49f4-ae3e-03c01fcae8b9", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.fs.rm(\"/mnt/pantherx/bronze/raw_prx_patient_status\", recurse=True)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9aff84ef-b24e-418c-b2a3-6424d9447373", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["dbutils.widgets.text(\"key\", \"1\", \"Enter key\")\n", "dbutils.widgets.text(\"source_system_id\", \"2\", \"Enter id\")\n", "params = dbutils.widgets.getAll()\n", "\n", "print(params)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "test", "widgets": {"key": {"currentValue": "1", "nuid": "3dd9a8e6-0dbf-4198-b35c-f6b2a7c24c0d", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "1", "label": "Enter key", "name": "key", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "1", "label": "Enter key", "name": "key", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}, "source_system_id": {"currentValue": "2", "nuid": "24f25d28-2a38-4629-a3e5-790facfa6bd3", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "2", "label": "Enter id", "name": "source_system_id", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "2", "label": "Enter id", "name": "source_system_id", "options": {"widgetType": "text", "autoCreated": null, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}