[2025-07-08T09:51:04.881+0000] {manager.py:470} INFO - Processing files using up to 2 processes at a time 
[2025-07-08T09:51:04.883+0000] {manager.py:471} INFO - Process each file at most once every 30 seconds
[2025-07-08T09:51:04.886+0000] {manager.py:472} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-08T09:51:04.888+0000] {manager.py:737} INFO - Searching for files in /opt/airflow/dags
[2025-07-08T09:51:04.923+0000] {manager.py:740} INFO - There are 2 files in /opt/airflow/dags
[2025-07-08T09:51:05.073+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                           PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  ----------
/opt/airflow/dags/example_dag.py    192  0.05s             0           0
/opt/airflow/dags/pnt_poc.py        189  0.07s             0           0
================================================================================
[2025-07-08T09:51:32.894+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.45s           2025-07-08T09:51:05
/opt/airflow/dags/pnt_poc.py                               0           1  0.14s           2025-07-08T09:51:05
================================================================================
[2025-07-08T09:52:08.310+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.22s           2025-07-08T09:52:06
/opt/airflow/dags/pnt_poc.py                               0           1  0.17s           2025-07-08T09:52:06
================================================================================
[2025-07-08T09:52:28.114+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.22s           2025-07-08T09:52:06
/opt/airflow/dags/pnt_poc.py                               0           1  0.17s           2025-07-08T09:52:06
================================================================================
[2025-07-08T09:52:35.291+0000] {manager.py:452} INFO - Exiting gracefully upon receiving signal 15
[2025-07-08T09:58:22.291+0000] {manager.py:470} INFO - Processing files using up to 2 processes at a time 
[2025-07-08T09:58:22.293+0000] {manager.py:471} INFO - Process each file at most once every 30 seconds
[2025-07-08T09:58:22.296+0000] {manager.py:472} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-08T09:58:22.298+0000] {manager.py:737} INFO - Searching for files in /opt/airflow/dags
[2025-07-08T09:58:22.384+0000] {manager.py:740} INFO - There are 2 files in /opt/airflow/dags
[2025-07-08T09:58:22.535+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                           PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  ----------
/opt/airflow/dags/example_dag.py    193  0.06s             0           0
/opt/airflow/dags/pnt_poc.py        190  0.08s             0           0
================================================================================
[2025-07-08T09:58:50.346+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.43s           2025-07-08T09:58:22
/opt/airflow/dags/pnt_poc.py                               0           1  0.16s           2025-07-08T09:58:22
================================================================================
[2025-07-08T09:59:18.070+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  -7.49s          2025-07-08T09:58:52
/opt/airflow/dags/pnt_poc.py                               0           1  -7.51s          2025-07-08T09:58:52
================================================================================
[2025-07-08T09:59:45.308+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.21s           2025-07-08T09:59:19
/opt/airflow/dags/pnt_poc.py                               0           1  -7.54s          2025-07-08T09:59:19
================================================================================
[2025-07-08T10:00:20.644+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                           PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/pnt_poc.py        278  0.03s             0           1  0.23s           2025-07-08T09:59:50
/opt/airflow/dags/example_dag.py    281  0.01s             1           0  0.21s           2025-07-08T09:59:50
================================================================================
