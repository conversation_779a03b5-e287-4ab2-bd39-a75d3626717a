[2025-07-08T09:51:04.881+0000] {manager.py:470} INFO - Processing files using up to 2 processes at a time 
[2025-07-08T09:51:04.883+0000] {manager.py:471} INFO - Process each file at most once every 30 seconds
[2025-07-08T09:51:04.886+0000] {manager.py:472} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-08T09:51:04.888+0000] {manager.py:737} INFO - Searching for files in /opt/airflow/dags
[2025-07-08T09:51:04.923+0000] {manager.py:740} INFO - There are 2 files in /opt/airflow/dags
[2025-07-08T09:51:05.073+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                           PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  ----------
/opt/airflow/dags/example_dag.py    192  0.05s             0           0
/opt/airflow/dags/pnt_poc.py        189  0.07s             0           0
================================================================================
[2025-07-08T09:51:32.894+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.45s           2025-07-08T09:51:05
/opt/airflow/dags/pnt_poc.py                               0           1  0.14s           2025-07-08T09:51:05
================================================================================
[2025-07-08T09:52:08.310+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.22s           2025-07-08T09:52:06
/opt/airflow/dags/pnt_poc.py                               0           1  0.17s           2025-07-08T09:52:06
================================================================================
[2025-07-08T09:52:28.114+0000] {manager.py:878} INFO - 
================================================================================
DAG File Processing Stats

File Path                         PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run
--------------------------------  -----  ---------  --------  ----------  --------------  -------------------
/opt/airflow/dags/example_dag.py                           1           0  0.22s           2025-07-08T09:52:06
/opt/airflow/dags/pnt_poc.py                               0           1  0.17s           2025-07-08T09:52:06
================================================================================
[2025-07-08T09:52:35.291+0000] {manager.py:452} INFO - Exiting gracefully upon receiving signal 15
