CREATE OR REPLACE TABLE hive_metastore.dwh_utils.DATA_SOURCE_TRACKER (
    ID BIGINT GENERATED ALWAYS AS IDENTITY,
    FILENAME VARCHAR(16777216),
    FILEPATH VARCHAR(16777216),
    ARCHIVE_FILEPATH VARCHAR(16777216),
    STATUS VARCHAR(25),
    ERROR_MESSAGE VARCHAR(16777216),
    DATE_CREATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
    DATE_LAST_UPDATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
    BLOB_FILE_UPLOAD_TIME TIMESTAMP,
    SOURCE_SYSTEM_ID INTEGER
) 
USING DELTA
LOCATION 'dbfs:/user/hive/warehouse/dwh_utils.db/new_data_source_tracker'
TBLPROPERTIES ('delta.feature.allowColumnDefaults' = 'supported');