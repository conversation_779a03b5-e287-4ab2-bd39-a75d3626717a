create or replace TABLE  hive_metastore.dwh_utils.STATUS_CATALOG (
	ID BIGINT GENERATED ALWAYS AS IDENTITY,
	STATUS VARCHAR(16777216) NOT NULL,
	DEFINITION VARCHAR(16777216),
	<PERSON><PERSON><PERSON>_CREATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
	DATE_LAST_UPDATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)USING DELTA
LOCATION 'dbfs:/user/hive/warehouse/dwh_utils.db/STATUS_CATALOG'
TBLPROPERTIES ('delta.feature.allowColumnDefaults' = 'supported');

create or replace TABLE hive_metastore.dwh_utils.SUB_STATUS_CATALOG (
	ID BIGINT GENERATED ALWAYS AS IDENTITY,
	STATUS_ID integer NOT NULL,
	SUB_STATUS VARCHAR(16777216) NOT NULL,
	DEFINITION VARCHAR(16777216),
	NOTE VARCHAR(16777216),
	<PERSON><PERSON><PERSON>_CREATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
	DATE_LAST_UPDATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP()
)USING DELTA
LOCATION 'dbfs:/user/hive/warehouse/dwh_utils.db/SUB_STATUS_CATALOG'
TBLPROPERTIES ('delta.feature.allowColumnDefaults' = 'supported');