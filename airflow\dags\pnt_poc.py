from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.providers.databricks.operators.databricks import DatabricksRunNowOperator
from datetime import datetime

# Define inline pipeline functions
def start_pipeline():
    print("Pipeline started.")

def end_pipeline():
    print("Pipeline completed.")

default_args = {
    'owner': 'airflow',
    'start_date': datetime(2025, 6, 3),
    'retries': 0
}

with DAG(
    dag_id='load_panther_data',
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    tags=['azure', 'databricks', 'pantheRx']
) as dag:
    
    jobs = [
        {"job_id": 246197370112797, "task_id": "extract_raw_data"},
        {"job_id": 76676181522119, "task_id": "transform_and_load_to_silver"},
        {"job_id": 644770267948752, "task_id": "validate_dqm_results"},
        {"job_id": 11667856668604, "task_id": "load_to_gold"},
        {"job_id": 705358919347421, "task_id": "archive_move_update_datasource"}
    ]
    
    with TaskGroup('start', tooltip='Pipeline Start') as start_group:
        start = PythonOperator(task_id='start', python_callable=start_pipeline)

    with TaskGroup('end', tooltip='Pipeline Completion') as end_group:
        end = PythonOperator(task_id='end', python_callable=end_pipeline)

    previous_task = start_group
    for job in jobs:

        current_task = DatabricksRunNowOperator(
            task_id=job["task_id"],
            databricks_conn_id="databricks_default",
            job_id=job["job_id"]
        )

        previous_task >> current_task
        previous_task = current_task

    previous_task >> end_group
