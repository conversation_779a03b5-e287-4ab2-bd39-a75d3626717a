{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9749fa02-e794-4768-84fe-97243bd73fe4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import sys\n", "from pyspark.sql.types import IntegerType\n", "\n", "# Replace this with the absolute path to the folder that contains 'udf'\n", "sys.path.append(\"/Workspace/Users/<USER>/Data%20Engineering_Automation/pantherx/udf/\")\n", "\n", "from age_calculations import calculate_age, calculate_age_by_rx_date\n", "\n", "calculate_age_udf = udf(calculate_age, IntegerType())\n", "spark.udf.register(\"calculate_age\", calculate_age_udf)\n", "\n", "calculate_age_by_rx_date_udf = udf(calculate_age_by_rx_date, IntegerType())\n", "spark.udf.register(\"calculate_age_by_rx_date\", calculate_age_by_rx_date_udf)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b4bc3beb-a1ba-402d-a3b3-438c7cc73978", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "-- status update\n", "MERGE INTO silver.curated_prx_patient_status AS ps\n", "USING (\n", "    WITH upd AS (\n", "        SELECT \n", "            scat.id, \n", "            staged.SP_PATIENT_ID AS sp_id, \n", "            staged.TRANSACTION_ID AS t_id\n", "        FROM silver.temp_raw_data_table AS staged \n", "        LEFT JOIN dwh_utils.status_catalog scat \n", "            ON UPPER(staged.STATUS) = UPPER(scat.status)\n", "    )\n", "    SELECT id, sp_id, t_id \n", "    FROM upd \n", "    GROUP BY id, sp_id, t_id\n", ") AS cte\n", "ON ps.sp_patient_id = cte.sp_id \n", "   AND ps.TRANSACTION_ID = cte.t_id\n", "   AND DATE(ps.date_created) = CURRENT_DATE()\n", "WHEN MATCHED THEN\n", "    UPDATE SET \n", "        ps.status = cte.id,\n", "        ps.DATE_LAST_UPDATED = current_timestamp();\n", "\n", "-- sub_status update\n", "\n", "MERGE INTO silver.curated_prx_patient_status AS ps\n", "USING (\n", "    WITH upd AS (\n", "        SELECT \n", "            ssc.ID, \n", "            staged.SP_PATIENT_ID AS sp_id, \n", "            staged.TRANSACTION_ID AS t_id,\n", "            ROW_NUMBER() OVER (PARTITION BY staged.SP_PATIENT_ID, staged.TRANSACTION_ID ORDER BY staged.SP_PATIENT_ID) AS rn\n", "        FROM silver.temp_raw_data_table AS staged \n", "        LEFT JOIN silver.curated_prx_patient_status ppss on  ppss.transaction_id=staged.TRANSACTION_ID  \n", "            left join dwh_utils.sub_status_catalog ssc on  ssc.status_id=ppss.status and UPPER(ssc.sub_status)\n", "            =UPPER(staged.SUB_STATUS)\n", "    )\n", "    SELECT id, sp_id, t_id \n", "    FROM upd where rn=1\n", "    GROUP BY id, sp_id, t_id\n", ") AS cte\n", "ON ps.sp_patient_id = cte.sp_id \n", "   AND ps.TRANSACTION_ID = cte.t_id\n", "   AND DATE(ps.date_created) = CURRENT_DATE()\n", "WHEN MATCHED THEN\n", "    UPDATE SET \n", "        ps.sub_status = cte.id,\n", "        ps.DATE_LAST_UPDATED = current_timestamp();\n", "\n", "-- zip updates\n", "UPDATE silver.curated_prx_patient_status LR\n", "              SET prescriber_zip = CASE WHEN LENGTH(LR.prescriber_zip) > 5 THEN LEFT(LR.prescriber_zip, 5) END\n", "              WHERE LENGTH(LR.prescriber_zip) > 5;\n", "\n", "UPDATE silver.curated_prx_patient_status LR\n", "              SET PATIENT_ZIP = CASE WHEN LENGTH(LR.PATIENT_ZIP) > 5 THEN LEFT(LR.PATIENT_ZIP, 5) END\n", "              WHERE LENGTH(LR.PATIENT_ZIP) > 5;\n", "\n", "-- update latest_indication\n", "MERGE INTO silver.curated_prx_patient_status AS ps\n", "USING (\n", "    WITH upd AS (\n", "        SELECT\n", "            SP_PATIENT_ID, \n", "            DIAG_CODE_1 AS LATEST_INDICATION, \n", "            STATUS_DATE_FORMATTED AS STATUS_DATE_FORMATTED,\n", "            DENSE_RANK() OVER (\n", "                PARTITION BY SP_PATIENT_ID \n", "                ORDER BY STATUS_DATE_FORMATTED DESC\n", "            ) AS DR\n", "        FROM silver.curated_prx_patient_status\n", "        WHERE DIAG_CODE_1 IS NOT NULL \n", "          AND IS_PROCESSED = TRUE\n", "    ),\n", "    deduped AS (\n", "        SELECT \n", "            SP_PATIENT_ID, \n", "            LATEST_INDICATION,\n", "            ROW_NUMBER() OVER (\n", "                PARTITION BY SP_PATIENT_ID \n", "                ORDER BY STATUS_DATE_FORMATTED DESC\n", "            ) AS RN\n", "        FROM upd\n", "        WHERE DR = 1\n", "    )\n", "    SELECT SP_PATIENT_ID, LATEST_INDICATION\n", "    FROM deduped\n", "    WHERE RN = 1\n", ") AS cte\n", "ON ps.SP_PATIENT_ID = cte.SP_PATIENT_ID\n", "   AND DATE(ps.date_created) = CURRENT_DATE()\n", "WHEN MATCHED THEN\n", "  UPDATE SET \n", "    ps.LATEST_INDICATION = cte.LATEST_INDICATION,\n", "    ps.DATE_LAST_UPDATED = current_timestamp();\n", "\n", "\n", "-- update patient_age and patient_age_on_rx\n", "MERGE INTO silver.CURATED_PRX_PATIENT_STATUS AS PRX\n", "USING (\n", "    SELECT \n", "        SP_PATIENT_ID,\n", "        calculate_age(PATIENT_DOB) AS CALC_PATIENT_AGE,\n", "        calculate_age_by_rx_date(PATIENT_DOB, RX_DATE) AS CALC_PATIENT_AGE_ON_RX\n", "    FROM (\n", "        SELECT\n", "            \n", "            SP_PATIENT_ID,\n", "            PATIENT_DOB,\n", "            RX_DATE,\n", "            ROW_NUMBER() OVER (\n", "                PARTITION BY SP_PATIENT_ID  \n", "                ORDER BY STATUS_DATE_FORMATTED DESC, TRANSACTION_SEQUENCE DESC\n", "            ) AS rn\n", "        FROM silver.CURATED_PRX_PATIENT_STATUS\n", "    ) AS ranked\n", "    WHERE ranked.rn = 1\n", ") AS LR\n", "ON PRX.SP_PATIENT_ID = LR.SP_PATIENT_ID \n", "WHEN MATCHED THEN\n", "UPDATE SET \n", "    PRX.PATIENT_AGE = LR.CALC_PATIENT_AGE,\n", "    PRX.PATIENT_AGE_ON_RX = LR.CALC_PATIENT_AGE_ON_RX;\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 7577023772853371, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "silver_updates", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}