create or replace TABLE X4_DW_SEC_PROD.DW_BC.PRX_PATIENT_STATUS (
	ID NUMBER(38,0) NOT NULL autoincrement start 1 increment 1 noorder,
	REC_DATE VARCHAR(15),
	PHARMACY_CODE VARCHAR(16777216),
	PHARMACY_NPI VARCHAR(10),
	TRANSACTION_TYPE VARCHAR(16777216),
	TRANSACTION_ID VARCHAR(16777216),
	TRANSACTION_SEQUENCE NUMBER(38,0),
	PROGRAM_NAME VARCHAR(16777216),
	PATIENT_FIRST_NAME VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_LAST_NAME VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	REFERRAL_SOURCE VARCHAR(10),
	REFERRAL_DATE VARCHAR(8),
	HUB_PATIENT_ID VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	SP_PATIENT_ID VARCHAR(16777216),
	PATIENT_DOB VARCHAR(8) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_GENDER VARCHAR(1),
	PATIENT_ADDRESS_LINE1 VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_ADDRESS_LINE2 VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_CITY VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_STATE VARCHAR(16777216),
	PATIENT_ZIP VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_PHONE VARCHAR(10) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_ALT_PHONE VARCHAR(10) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_EMAIL VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PATIENT_OK_FOR_MESSAGE VARCHAR(1),
	CAREGIVER_NAME VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	CAREGIVER_PHONE VARCHAR(10) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	CAREGIVER_EMAIL VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	CAREGIVER_OK_FOR_MESSAGE VARCHAR(1),
	PATIENT_EAP VARCHAR(1),
	DIAG_CODE_1 VARCHAR(16777216),
	DIAG_CODE_2 VARCHAR(16777216),
	GENETIC_TEST_COMPLETED VARCHAR(1),
	GENETIC_TEST_RESULT VARCHAR(16777216),
	FAMILY_HISTORY_OF_WHIM VARCHAR(1),
	CURRENT_THERAPIES VARCHAR(16777216),
	PREVIOUS_THERAPIES VARCHAR(16777216),
	STATUS_DATE VARCHAR(15),
	STATUS NUMBER(38,0),
	SUB_STATUS NUMBER(38,0),
	PROGRAM_CONSENT VARCHAR(1),
	PROGRAM_CONSENT_DATE VARCHAR(8),
	HIPAA_AUTH_STATUS VARCHAR(1),
	HIPAA_AUTH_DATE VARCHAR(8),
	MARKETING_CONSENT_STATUS VARCHAR(1),
	MARKETING_CONSENT_DATE VARCHAR(8),
	PRESCRIBER_LAST_NAME VARCHAR(16777216),
	PRESCRIBER_FIRST_NAME VARCHAR(16777216),
	PRESCRIBER_ADDRESS_LINE1 VARCHAR(16777216),
	PRESCRIBER_ADDRESS_LINE2 VARCHAR(16777216),
	PRESCRIBER_CITY VARCHAR(16777216),
	PRESCRIBER_STATE VARCHAR(16777216),
	PRESCRIBER_ZIP VARCHAR(16777216),
	PRESCRIBER_PHONE NUMBER(38,0),
	PRESCRIBER_FAX NUMBER(38,0),
	PRESCRIBER_NPI VARCHAR(16777216),
	PRESCRIBER_DEA VARCHAR(16777216),
	PRESCRIBER_SPECIALTY VARCHAR(16777216),
	FACILITY_NAME VARCHAR(16777216),
	CONTACT_NAME VARCHAR(16777216),
	CONTACT_EMAIL VARCHAR(16777216),
	RX_DATE VARCHAR(8),
	RX_NUMBER VARCHAR(16777216),
	RX_REFILLS_ALLOWED NUMBER(38,0),
	RX_FILL_NUMBER NUMBER(38,0),
	REFILLS_REMAINING NUMBER(38,0),
	DAW NUMBER(38,0),
	NDC VARCHAR(11),
	PRODUCT_NAME VARCHAR(16777216),
	QUANTITY_DISPENSED NUMBER(38,0),
	UNIT_OF_MEASURE VARCHAR(16777216),
	DAYS_SUPPLY NUMBER(38,0),
	DATE_SHIPPED VARCHAR(8),
	PRODUCT_LOT VARCHAR(16777216),
	PRODUCT_EXP_DATE VARCHAR(8),
	SHIP_TO_LOCATION VARCHAR(10),
	SHIP_CARRIER VARCHAR(16777216),
	SHIP_TRACKING_NO VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	PRIMARY_COVERAGE_TYPE VARCHAR(1),
	PRIMARY_PAYOR_NAME VARCHAR(16777216),
	PRIMARY_PAYOR_SEGMENT VARCHAR(25),
	PRIMARY_PA_REQUIRED VARCHAR(1),
	PRIMARY_PA_AUTH_START VARCHAR(8),
	PRIMARY_PA_AUTH_END VARCHAR(8),
	PRIMARY_PBM_NAME VARCHAR(16777216),
	PRIMARY_PBM_BIN VARCHAR(10),
	PRIMARY_PBM_PCN VARCHAR(16777216),
	PRIMARY_PBM_GROUP VARCHAR(16777216),
	PRIMARY_PBM_PLAN_ID VARCHAR(16777216),
	SECONDARY_COVERAGE_TYPE VARCHAR(1),
	SECONDARY_PAYOR_NAME VARCHAR(16777216),
	SECONDARY_PAYOR_TYPE VARCHAR(25),
	SECONDARY_PA_REQUIRED VARCHAR(1),
	SECONDARY_PA_AUTH_START VARCHAR(8),
	SECONDARY_PA_AUTH_END VARCHAR(8),
	SECONDARY_PBM_NAME VARCHAR(16777216),
	SECONDARY_PBM_BIN VARCHAR(10),
	SECONDARY_PBM_PCN VARCHAR(16777216),
	SECONDARY_PBM_GROUP VARCHAR(16777216),
	SECONDARY_PBM_PLAN_ID VARCHAR(16777216),
	TERTIARY_COVERAGE_TYPE VARCHAR(1),
	TERTIARY_PAYOR_NAME VARCHAR(16777216),
	TERTIARY_PAYOR_TYPE VARCHAR(25),
	TERTIARY_PA_REQUIRED VARCHAR(1),
	TERTIARY_PA_AUTH_START VARCHAR(8),
	TERTIARY_PA_AUTH_END VARCHAR(8),
	TERTIARY_PBM_NAME VARCHAR(16777216),
	TERTIARY_PBM_BIN VARCHAR(10),
	TERTIARY_PBM_PCN VARCHAR(16777216),
	TERTIARY_PBM_GROUP VARCHAR(16777216),
	TERTIARY_PBM_PLAN_ID VARCHAR(16777216),
	CLAIM_TYPE VARCHAR(1),
	PATIENT_COPAY_AMT FLOAT,
	COPAY_ASSIST_FLAG VARCHAR(1),
	COPAY_ASSIST_ID VARCHAR(16777216) WITH MASKING POLICY X4_DW_SEC_PROD.DW_BC.DATA_MASKING,
	COPAY_ASSIST_AMT FLOAT,
	PATIENT_FINAL_OOP FLOAT,
	IS_PROCESSED BOOLEAN DEFAULT FALSE,
	DATASOURCE_ID NUMBER(38,0),
	DATE_CREATED TIMESTAMP_NTZ(9) DEFAULT CURRENT_TIMESTAMP(),
	DATE_LAST_UPDATED TIMESTAMP_NTZ(9) DEFAULT CURRENT_TIMESTAMP(),
	REC_DATE_FORMATTED TIMESTAMP_NTZ(9),
	REFERRAL_DATE_FORMATTED DATE,
	STATUS_DATE_FORMATTED TIMESTAMP_NTZ(9),
	PROGRAM_CONSENT_DATE_FORMATTED DATE,
	HIPAA_AUTH_DATE_FORMATTED DATE,
	MARKETING_CONSENT_DATE_FORMATTED DATE,
	RX_DATE_FORMATTED DATE,
	DATE_SHIPPED_FORMATTED DATE,
	PRODUCT_EXP_DATE_FORMATTED DATE,
	HCP_SIGNATURE_ON_NEW_RX VARCHAR(1),
	PRIMARY_PA_AUTH_START_FORMATTED DATE,
	PRIMARY_PA_AUTH_END_FORMATTED DATE,
	SECONDARY_PA_AUTH_START_FORMATTED DATE,
	SECONDARY_PA_AUTH_END_FORMATTED DATE,
	TERTIARY_PA_AUTH_START_FORMATTED DATE,
	TERTIARY_PA_AUTH_END_FORMATTED DATE,
	PATIENT_AGE NUMBER(38,0),
	TOTAL_DAILY_DOSE NUMBER(38,0),
	PATIENT_WEIGHT_KG FLOAT,
	IS_DELETED BOOLEAN DEFAULT FALSE,
	LATEST_INDICATION VARCHAR(16777216),
	PATIENT_AGE_ON_RX NUMBER(38,0),
	primary key (ID),
	constraint FK_STATUS foreign key (STATUS) references X4_DW_SEC_DEV.DW_BC.STATUS_CATALOG(ID)
);