from pyspark.sql.functions import udf
from pyspark.sql.types import IntegerType
from datetime import datetime, date
# from pyspark.sql import SparkSession

# spark = SparkSession.builder.getOrCreate()
def calculate_age(PATIENT_DOB):
    try:
        if PATIENT_DOB is None:
            return None
        
        dob_str = str(PATIENT_DOB).strip()
        today = date.today()

        if len(dob_str) == 4:
            birth_year = int(dob_str)
            return today.year - birth_year

        elif len(dob_str) == 8:
            born = datetime.strptime(dob_str, "%Y%m%d").date()
        else:
            born = datetime.strptime(dob_str, "%Y-%m-%d").date()

        return today.year - born.year - ((today.month, today.day) < (born.month, born.day))
    
    except Exception:
        return None
    

def calculate_age_by_rx_date(PATIENT_DOB, RX_DATE):
    try:
        if PATIENT_DOB is None or RX_DATE is None:
            return None
        
        dob_str = str(PATIENT_DOB).strip()
        rx_str = str(RX_DATE).strip()

        if len(rx_str) == 8:
            rx_date = datetime.strptime(rx_str, "%Y%m%d").date()
        else:
            rx_date = datetime.strptime(rx_str, "%Y-%m-%d").date()

        if len(dob_str) == 4:
            birth_year = int(dob_str)
            return rx_date.year - birth_year

        elif len(dob_str) == 8:
            born = datetime.strptime(dob_str, "%Y%m%d").date()
        else:
            born = datetime.strptime(dob_str, "%Y-%m-%d").date()

        return rx_date.year - born.year - ((rx_date.month, rx_date.day) < (born.month, born.day))

    except Exception:
        return None


