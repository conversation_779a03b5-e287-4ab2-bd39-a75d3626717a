CREATE OR REPLACE VIEW dwh_dqm.prx_dqm_errors AS
SELECT
  PROCESSING_ERROR,
  sp_patient_id,
  transaction_id,
  date_created,
  id,
  'yes' AS is_critical,
  CURRENT_TIMESTAMP() AS error_timestamp
  from silver.curated_prx_patient_status where IS_PROCESSED = false 

UNION ALL

SELECT
  CONCAT_WS(', ', COLLECT_LIST(error_message)) AS error_message,
  sp_patient_id,
  transaction_id,
  date_created,
  id,
  'no' AS is_critical,
  CURRENT_TIMESTAMP() AS error_timestamp
  from dwh_dqm.prx_dqm_view where is_critical = 'no'
  GROUP BY sp_patient_id, transaction_id, date_created, id;
  

