CREATE OR <PERSON><PERSON><PERSON>CE TABLE hive_metastore.gold.prx_patient_status (
  ID BIGINT GENERATED ALWAYS AS IDENTITY,
  REC_DATE STRING,
  PHARMACY_CODE STRING,
  PHARMACY_NPI STRING,
  TRANSACTION_TYPE STRING,
  TRANSACTION_ID STRING,
  TRANS<PERSON>TION_SEQUENCE INTEGER,
  <PERSON><PERSON><PERSON>AM_NAME STRING,
  PATIENT_FIRST_NAME STRING,
  PATIENT_LAST_NAME STRING,
  REFERRAL_SOURCE STRING,
  REFERRAL_DATE STRING,
  HUB_PATIENT_ID STRING,
  SP_PATIENT_ID STRING,
  PATIENT_DOB STRING,
  PATIENT_GENDER STRING,
  PATIENT_WEIGHT_KG DOUBLE,
  PATIENT_ADDRESS_LINE1 STRING,
  PATIENT_ADDRESS_LINE2 STRING,
  PATIENT_CITY STRING,
  PATIENT_STATE STRING,
  PATIENT_ZIP STRING,
  PATIENT_PHONE STRING,
  PATIENT_ALT_PHONE STRING,
  PATIENT_EMAIL STRING,
  PATIENT_OK_FOR_MESSAGE STRING,
  CAREGIVER_NAME STRING,
  CAREGIVER_PHONE STRING,
  CAREGIVER_EMAIL STRING,
  CAREGIVER_OK_FOR_MESSAGE STRING,
  PATIENT_EAP STRING,
  DIAG_CODE_1 STRING,
  DIAG_CODE_2 STRING,
  GENETIC_TEST_COMPLETED STRING,
  GENETIC_TEST_RESULT STRING,
  FAMILY_HISTORY_OF_WHIM STRING,
  CURRENT_THERAPIES STRING,
  PREVIOUS_THERAPIES STRING,
  STATUS_DATE STRING,
  STATUS BIGINT,
  SUB_STATUS BIGINT,
  PROGRAM_CONSENT STRING,
  PROGRAM_CONSENT_DATE STRING,
  HIPAA_AUTH_STATUS STRING,
  HIPAA_AUTH_DATE STRING,
  MARKETING_CONSENT_STATUS STRING,
  MARKETING_CONSENT_DATE STRING,
  PRESCRIBER_LAST_NAME STRING,
  PRESCRIBER_FIRST_NAME STRING,
  PRESCRIBER_ADDRESS_LINE1 STRING,
  PRESCRIBER_ADDRESS_LINE2 STRING,
  PRESCRIBER_CITY STRING,
  PRESCRIBER_STATE STRING,
  PRESCRIBER_ZIP STRING,
  PRESCRIBER_PHONE BIGINT,
  PRESCRIBER_FAX BIGINT,
  PRESCRIBER_NPI STRING,
  PRESCRIBER_DEA STRING,
  PRESCRIBER_SPECIALTY STRING,
  FACILITY_NAME STRING,
  CONTACT_NAME STRING,
  CONTACT_EMAIL STRING,
  HCP_SIGNATURE_ON_NEW_RX STRING,
  RX_DATE STRING,
  RX_NUMBER STRING,
  RX_REFILLS_ALLOWED BIGINT,
  RX_FILL_NUMBER BIGINT,
  REFILLS_REMAINING BIGINT,
  DAW BIGINT,
  NDC STRING,
  PRODUCT_NAME STRING,
  QUANTITY_DISPENSED BIGINT,
  TOTAL_DAILY_DOSE BIGINT,
  UNIT_OF_MEASURE STRING,
  DAYS_SUPPLY BIGINT,
  DATE_SHIPPED STRING,
  PRODUCT_LOT STRING,
  PRODUCT_EXP_DATE STRING,
  SHIP_TO_LOCATION STRING,
  SHIP_CARRIER STRING,
  SHIP_TRACKING_NO STRING,
  PRIMARY_COVERAGE_TYPE STRING,
  PRIMARY_PAYOR_NAME STRING,
  PRIMARY_PAYOR_SEGMENT STRING,
  PRIMARY_PA_REQUIRED STRING,
  PRIMARY_PA_AUTH_START STRING,
  PRIMARY_PA_AUTH_END STRING,
  PRIMARY_PBM_NAME STRING,
  PRIMARY_PBM_BIN STRING,
  PRIMARY_PBM_PCN STRING,
  PRIMARY_PBM_GROUP STRING,
  PRIMARY_PBM_PLAN_ID STRING,
  SECONDARY_COVERAGE_TYPE STRING,
  SECONDARY_PAYOR_NAME STRING,
  SECONDARY_PAYOR_TYPE STRING,
  SECONDARY_PA_REQUIRED STRING,
  SECONDARY_PA_AUTH_START STRING,
  SECONDARY_PA_AUTH_END STRING,
  SECONDARY_PBM_NAME STRING,
  SECONDARY_PBM_BIN STRING,
  SECONDARY_PBM_PCN STRING,
  SECONDARY_PBM_GROUP STRING,
  SECONDARY_PBM_PLAN_ID STRING,
  TERTIARY_COVERAGE_TYPE STRING,
  TERTIARY_PAYOR_NAME STRING,
  TERTIARY_PAYOR_TYPE STRING,
  TERTIARY_PA_REQUIRED STRING,
  TERTIARY_PA_AUTH_START STRING,
  TERTIARY_PA_AUTH_END STRING,
  TERTIARY_PBM_NAME STRING,
  TERTIARY_PBM_BIN STRING,
  TERTIARY_PBM_PCN STRING,
  TERTIARY_PBM_GROUP STRING,
  TERTIARY_PBM_PLAN_ID STRING,
  CLAIM_TYPE STRING,
  PATIENT_COPAY_AMT DOUBLE,
  COPAY_ASSIST_FLAG STRING,
  COPAY_ASSIST_ID STRING,
  COPAY_ASSIST_AMT DOUBLE,
  PATIENT_FINAL_OOP DOUBLE,
  DATASOURCE_ID BIGINT,
  REC_DATE_FORMATTED TIMESTAMP,
  REFERRAL_DATE_FORMATTED DATE,
  STATUS_DATE_FORMATTED TIMESTAMP,
  PROGRAM_CONSENT_DATE_FORMATTED DATE,
  HIPAA_AUTH_DATE_FORMATTED DATE,
  MARKETING_CONSENT_DATE_FORMATTED DATE,
  RX_DATE_FORMATTED DATE,
  DATE_SHIPPED_FORMATTED DATE,
  PRODUCT_EXP_DATE_FORMATTED DATE,
  PRIMARY_PA_AUTH_START_FORMATTED DATE,
  PRIMARY_PA_AUTH_END_FORMATTED DATE,
  SECONDARY_PA_AUTH_START_FORMATTED DATE,
  SECONDARY_PA_AUTH_END_FORMATTED DATE,
  TERTIARY_PA_AUTH_START_FORMATTED DATE,
  TERTIARY_PA_AUTH_END_FORMATTED DATE,
  PATIENT_AGE BIGINT,
  LATEST_INDICATION STRING,
  PATIENT_AGE_ON_RX BIGINT,
  DATE_CREATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
  DATE_LAST_UPDATED TIMESTAMP DEFAULT CURRENT_TIMESTAMP(),
  IS_PROCESSED BOOLEAN DEFAULT TRUE,
  IS_DELETED BOOLEAN DEFAULT FALSE
)
USING DELTA
LOCATION 'dbfs:/user/hive/warehouse/gold.db/PRX_PATIENT_STATUS'
TBLPROPERTIES (
  'delta.feature.allowColumnDefaults' = 'supported'
);
