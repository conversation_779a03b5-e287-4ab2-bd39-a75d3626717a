create or replace TABLE hive_metastore.dwh_utils.SOURCE_SYSTEM_MASTER (
	ID BIGINT GENERATED ALWAYS AS IDENTITY,
	SYSTEMNAME VARCHAR(50) NOT NULL,
	DATECREATED TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
	DATELASTUPDATED TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),
	TABLE_NAME VARCHAR(16777216),
	SHORT_DESCRIPTION VARCHAR(500)
)USING DELTA
LOCATION 'dbfs:/user/hive/warehouse/dwh_utils.db/SOURCE_SYSTEM_MASTER'
TBLPROPERTIES ('delta.feature.allowColumnDefaults' = 'supported');

insert into hive_metastore.dwh_utils.SOURCE_SYSTEM_MASTER(systemname,table_name,short_description) values('pantherx','prx_patient_status','pantherx data');