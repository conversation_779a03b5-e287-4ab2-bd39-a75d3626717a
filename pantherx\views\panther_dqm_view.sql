%sql
CREATE OR REPLACE VIEW dwh_dqm.prx_dqm_view AS
SELECT
  error_message,
  CONCAT(error_column, ' Validation') AS error_type,
  column_value AS error_value,
  sp_id AS sp_patient_id,
  t_id AS transaction_id,
  date_id AS date_created,
  prxid AS id,
  'pantheRx' AS source,
  'yes' AS is_critical,
  CURRENT_TIMESTAMP() AS error_timestamp
FROM (
  SELECT
    'PRESCRIBER_ZIP contains blank or NULL values or not 5 digit number' AS error_message,
    'PRESCRIBER_ZIP' AS error_column,
    CAST(PRESCRIBER_ZIP AS STRING) AS column_value,
    CAST(sp_patient_id AS STRING) AS sp_id,
    CAST(transaction_id AS STRING) AS t_id,
    date_created AS date_id,
    id AS prxid
  FROM silver.curated_prx_patient_status
  WHERE
    PRESCRIBER_ZIP IS NULL
    OR TRIM(PRESCRIBER_ZIP) = ''
    OR LENGTH(PRESCRIBER_ZIP) <> 5
    OR TRY_CAST(PRESCRIBER_ZIP AS INT) IS NULL
    OR PRESCRIBER_ZIP = '00000'

  UNION ALL

  SELECT
    'COPAY_ASSIST_FLAG contains blank or NULL values or not in Y, N, U format',
    'COPAY_ASSIST_FLAG',
    CAST(COPAY_ASSIST_FLAG AS STRING),
    CAST(sp_patient_id AS STRING),
    CAST(transaction_id AS STRING),
    date_created,
    id
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true
    AND (
      COPAY_ASSIST_FLAG IS NULL
      OR TRIM(COPAY_ASSIST_FLAG) = ''
      OR COPAY_ASSIST_FLAG NOT IN ('Y', 'N', 'U')
    )

  UNION ALL

  SELECT
  'DIAG_CODE_1 contains blank or NULL values or not in WHIM, Other',
  'DIAG_CODE_1' ,
  CAST(DIAG_CODE_1 AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  date_created,
  id
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true
    AND (
      DIAG_CODE_1 IS NULL
      OR TRIM(DIAG_CODE_1) = ''
      OR DIAG_CODE_1 NOT IN ('WHIM', 'Other')
    )

  UNION ALL

  SELECT
  'GENETIC_TEST_COMPLETED contains blank or NULL values or not in Y, N, U format',
  'GENETIC_TEST_COMPLETED',
  CAST(GENETIC_TEST_COMPLETED AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  date_created,
  id
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true
    AND (
      GENETIC_TEST_COMPLETED IS NULL
      OR TRIM(GENETIC_TEST_COMPLETED) = ''
      OR GENETIC_TEST_COMPLETED NOT IN ('Y', 'N', 'U')
    )

  UNION ALL

  SELECT 
  'HCP_SIGNATURE_ON_NEW_RX contains blank or NULL values or not in Y, N format',
  'HCP_SIGNATURE_ON_NEW_RX',
  cast(HCP_SIGNATURE_ON_NEW_RX as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
  FROM silver.curated_prx_patient_status
  WHERE 
    HCP_SIGNATURE_ON_NEW_RX IS NULL
    OR trim(HCP_SIGNATURE_ON_NEW_RX) = ''
    OR upper(trim(HCP_SIGNATURE_ON_NEW_RX)) NOT IN ('Y', 'N')

  UNION ALL

  -- NDC validation: null, blank, not 11-digit, or non-numeric
  SELECT 
    'NDC contains blank or NULL values or not in 11 digit format' AS error_message,
    'NDC' AS column_name,
    CAST(NDC AS STRING) AS ndc_value,
    CAST(sp_patient_id AS STRING) AS sp_patient_id,
    CAST(transaction_id AS STRING) AS transaction_id,
    date_created,
    id
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true
    AND (
      NDC IS NULL
      OR TRIM(NDC) = ''
      OR LENGTH(regexp_replace(NDC, '[^0-9]', '')) <> 11
    )



  UNION ALL

  -- PATIENT_WEIGHT_KG validation: null, blank, or non-numeric
  SELECT 
    'PATIENT_WEIGHT_KG contains blank or NULL values or not in Number format',
    'PATIENT_WEIGHT_KG',
    cast(PATIENT_WEIGHT_KG as string),
    cast(sp_patient_id as string),
    cast(transaction_id as string),
    date_created,
    id
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true 
    AND (
      PATIENT_WEIGHT_KG IS NULL 
      OR trim(PATIENT_WEIGHT_KG) = '' 
      OR cast(PATIENT_WEIGHT_KG as double) IS NULL
    )


  UNION ALL

  SELECT 
  'PRIMARY_PAYOR_NAME contains blank or NULL values',
  'PRIMARY_PAYOR_NAME',
  cast(t1.PRIMARY_PAYOR_NAME as string),
  cast(t1.sp_patient_id as string),
  cast(t1.transaction_id as string),
  t1.date_created,
  t1.id
  FROM silver.curated_prx_patient_status t1
  LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id
  LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id
  WHERE 
    t1.is_processed = true
    AND upper(s1.status) <> 'PENDING'
    AND (
      t1.PRIMARY_PAYOR_NAME IS NULL 
      OR trim(t1.PRIMARY_PAYOR_NAME) = ''
    )

  UNION ALL

  -- PROGRAM_CONSENT validation
SELECT 
  'PROGRAM_CONSENT contains blank or NULL values or not in Y, N, U format',
  'PROGRAM_CONSENT',
  cast(PROGRAM_CONSENT as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND (
    PROGRAM_CONSENT IS NULL 
    OR trim(PROGRAM_CONSENT) = '' 
    OR PROGRAM_CONSENT NOT IN ('Y', 'N', 'U')
  )

UNION ALL

-- SP_PATIENT_ID format validation
SELECT 
  'SP_PATIENT_ID contains blank or NULL values or not in proper format',
  'SP_PATIENT_ID',
  cast(SP_PATIENT_ID as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND (
    SP_PATIENT_ID IS NULL 
    OR trim(SP_PATIENT_ID) = ''
    OR NOT regexp_like(SP_PATIENT_ID, '^[a-zA-Z0-9-]+$')
  )

UNION ALL

-- STATUS validation
SELECT 
  'STATUS contains blank or NULL values or may not from given values',
  'STATUS',
  cast(prx.status as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status prx
WHERE is_processed = true 
  AND (
    prx.status IS NULL 
    OR trim(prx.status) = '' 
    OR prx.status NOT IN (SELECT id FROM dwh_utils.status_catalog)
  )

UNION ALL

-- SUB_STATUS validation
SELECT 
  'SUB_STATUS contains blank or NULL values or may not from given values',
  'SUB_STATUS',
  cast(prx.sub_status as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status prx
WHERE is_processed = true 
  AND (
    prx.sub_status IS NULL 
    OR trim(prx.sub_status) = '' 
    OR prx.sub_status NOT IN (SELECT id FROM dwh_utils.sub_status_catalog)
  )

UNION ALL

-- STATUS_DATE format validation
SELECT 
  'STATUS_DATE contains blank or NULL values or not in YYYYMMDDHH24MISS format',
  'STATUS_DATE',
  cast(STATUS_DATE as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND (
    STATUS_DATE IS NULL 
    OR trim(STATUS_DATE) = '' 
    OR to_timestamp(STATUS_DATE, 'yyyyMMddHHmmss') IS NULL
  )

UNION ALL
-- Copay Assist Flag = Y but copay_assist_amt is NULL
SELECT 
  'Copay Assist Amount should be provided when Copay Assist Flag is "Y"',
  'copay_assist_amt',
  cast(copay_assist_amt as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND copay_assist_flag = 'Y'
  AND copay_assist_amt IS NULL

UNION ALL

-- Copay Assist Flag is U or N but copay_assist_amt is NOT NULL
SELECT 
  'Copay Assist Amount should be NULL when Copay Assist Flag is "U" or "N"',
  'copay_assist_amt',
  cast(copay_assist_amt as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND copay_assist_flag IN ('U', 'N')
  AND copay_assist_amt IS NOT NULL

UNION ALL

-- date_shipped is not in valid YYYYMMDD format
SELECT 
  'date_shipped must be in the format YYYYMMDD',
  'date_shipped',
  cast(date_shipped as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND date_shipped IS NOT NULL
  AND (
    length(date_shipped) <> 8 
    OR to_date(date_shipped, 'yyyyMMdd') IS NULL
  )
UNION ALL
-- Validation: date_shipped must not be blank for ACTIVE/SHIPMENT or ACTIVE/MATERIALS
SELECT 
  'date_shipped must be available and not blank when the staus/substatus combination is ACTIVE/SHIPMENT or ACTIVE/MATERIALS',
  'date_shipped',
  cast(date_shipped as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM (
  SELECT t2.date_shipped, s3.status, s4.sub_status, t2.is_processed, t2.sp_patient_id, t2.transaction_id, t2.date_created, t2.id
  FROM silver.curated_prx_patient_status t2
  LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
  LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
  WHERE s3.status = 'Active' AND s4.sub_status IN ('SHIPMENT', 'MATERIALS')
) 
WHERE is_processed = true 
  AND (date_shipped IS NULL OR trim(date_shipped) = '')

UNION ALL

-- Validation: Genetic_Test_Result required when Genetic_Test_Completed = 'Y'
SELECT 
  'Genetic_Test_Results cannot be null when Genetic_Test_Completed = Y',
  'Genetic_Test_Result',
  cast(genetic_test_result as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND genetic_test_completed = 'Y'
  AND (genetic_test_result IS NULL OR trim(genetic_test_result) = '')

UNION ALL

-- Validation: patient_age_on_rx required when rx_date is present
SELECT 
  'patient_age_on_rx must be present when Rx Date is provided',
  'PATIENT_AGE_ON_RX',
  cast(patient_age_on_rx as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND rx_date IS NOT NULL
  AND (patient_age_on_rx IS NULL OR cast(patient_age_on_rx as double) IS NULL)

UNION ALL

-- Validation: prescriber_npi valid format
SELECT 
  'prescriber_npi must be present in valid formate',
  'prescriber_npi',
  cast(concat_ws(', ', prescriber_npi, sp_patient_id) as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    length(prescriber_npi) <> 10 
    OR cast(prescriber_npi as int) IS NULL
    OR prescriber_npi = '**********'
  )

UNION ALL

-- Validation: prescriber_npi or prescriber_dea must be present
SELECT 
  'prescriber_npi must be present and not blank if blank verify prescriber_dea is not blank',
  'prescriber_npi',
  cast(prescriber_npi as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (prescriber_npi IS NULL OR trim(prescriber_npi) = '')
  AND (prescriber_dea IS NULL OR trim(prescriber_dea) = '')

UNION ALL

-- Validation: program_consent_date required in format YYYYMMDD when program_consent = 'Y'
SELECT 
  'program_consent_date must be in yyyymmmdd format and not blank when program_consent is "Y" ',
  'program_consent_date',
  cast(program_consent_date as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND program_consent = 'Y'
  AND (
    program_consent_date IS NULL
    OR trim(program_consent_date) = ''
    OR to_date(program_consent_date, 'yyyyMMdd') IS NULL
  )

UNION ALL

-- Validation: RX should be unique by rx_date
SELECT 
  'Each Rx should have a unique RX Date',
  'rx_date',
  cast(rx_date as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND rx_date_formatted IS NOT NULL
GROUP BY rx_date, date_created, id, rx_number, sp_patient_id, transaction_id
HAVING count(distinct sp_patient_id) > 1

UNION ALL

-- Validation: sp_patient_id consistency per patient
SELECT 
  'For each patient sp_patient_id should be consistent and unique',
  'sp_patient_id',
  cast(sp_patient_id as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM (
  SELECT distinct sp_patient_id, transaction_id, date_created, id,
         count(distinct concat(trim(sp_patient_id), upper(trim(patient_first_name)), upper(trim(patient_last_name)))) as combination
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true AND hipaa_auth_status = 'Y'
  GROUP BY sp_patient_id, transaction_id, date_created, id
  HAVING combination > 1
)

UNION ALL

-- Validation: Duplicate key combinations across sp_patient_id, status, sub_status, etc.
SELECT 
  'Duplicate combination found sp_patient_id, status, sub_status, transaction_id, transaction_sequence, transaction_type, status_date_formatted',
  'sp_patient_id, status, sub_status, transaction_id, transaction_sequence',
  concat_ws(' | ', 
    cast(a.sp_patient_id as string),
    cast(a.status as string),
    cast(a.sub_status as string),
    cast(a.transaction_id as string),
    cast(a.transaction_sequence as string),
    cast(a.transaction_type as string),
    cast(a.status_date_formatted as string)
  ),
  cast(a.sp_patient_id as string),
  cast(a.transaction_id as string),
  a.date_created,
  a.id
FROM silver.curated_prx_patient_status a
WHERE a.is_processed = true
  AND EXISTS (
    SELECT 1
    FROM silver.curated_prx_patient_status b
    WHERE a.sp_patient_id = b.sp_patient_id
      AND a.status = b.status
      AND a.sub_status = b.sub_status
      AND a.transaction_id = b.transaction_id
      AND a.transaction_sequence = b.transaction_sequence
      AND a.transaction_type = b.transaction_type
      AND a.status_date_formatted = b.status_date_formatted
      AND a.id <> b.id
  )

UNION ALL

-- Validation: STATUS_DATE should not be in future
SELECT 
  'Status Date should never be greater than current date',
  'STATUS_DATE',
  cast(status_date as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND to_timestamp(status_date, 'yyyyMMddHHmmss') > current_date

UNION ALL

-- Validation: primary_payor_segment must be from valid values
SELECT 
  'primary_payor_segment is not from COMMERCIAL, MEDICAID, MEDICARE, OTHER, PAP, FEDERAL, TRICARE, VA, CASH, UNINSURED, BRIDGE, QUICK START value',
  'primary_payor_segment',
  cast(primary_payor_segment as string),
  cast(t1.sp_patient_id as string),
  cast(t1.transaction_id as string),
  t1.date_created,
  t1.id
FROM silver.curated_prx_patient_status t1
LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id
WHERE 
  t1.is_processed = true
  AND upper(s1.status) <> 'PENDING'
  AND (
    primary_payor_segment IS NULL
    OR upper(primary_payor_segment) NOT IN (
      'COMMERCIAL', 'MEDICAID', 'MEDICARE', 'OTHER', 'PAP', 'FEDERAL',
      'TRICARE', 'VA', 'CASH', 'UNINSURED', 'BRIDGE', 'QUICK START'
    )
  )

UNION ALL

-- Validation: TOTAL_DAILY_DOSE should be present and numeric when status is Active
SELECT 
  'TOTAL_DAILY_DOSE contains blank or NULL values or not in Number Format',
  'TOTAL_DAILY_DOSE',
  cast(TOTAL_DAILY_DOSE as string),
  cast(t1.sp_patient_id as string),
  cast(t1.transaction_id as string),
  t1.date_created,
  t1.id
FROM silver.curated_prx_patient_status t1
LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id
WHERE 
  t1.is_processed = true
  AND s1.status = 'Active'
  AND (
    TOTAL_DAILY_DOSE IS NULL
    OR trim(TOTAL_DAILY_DOSE) = ''
    OR cast(TOTAL_DAILY_DOSE as double) IS NULL
  )

UNION ALL

-- Validation: TOTAL_DAILY_DOSE ratio mismatch with quantity_dispensed and days_supply
SELECT 
  'Total Daily Dose should not be greater than quantity_dispensed and days_supply for active shipment.',
  'TOTAL_DAILY_DOSE',
  cast(total_daily_dose as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  t2.date_created,
  t2.id
FROM silver.curated_prx_patient_status t2
LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
WHERE t2.is_processed = true
  AND s3.status = 'Active'
  AND s4.sub_status = 'SHIPMENT'
  AND days_supply IS NOT NULL
  AND days_supply <> 0
  AND (total_daily_dose / 100.0) <> (quantity_dispensed / days_supply)

UNION ALL

-- Validation: DAYS_SUPPLY null or not numeric for ACTIVE/SHIPMENT
SELECT 
  'DAYS_SUPPLY contains blank or NULL values or not in Number format when ACTIVE/SHIPMENT',
  'DAYS_SUPPLY',
  cast(days_supply as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  t2.date_created,
  t2.id
FROM silver.curated_prx_patient_status t2
LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
WHERE t2.is_processed = true
  AND s3.status = 'Active'
  AND s4.sub_status = 'SHIPMENT'
  AND (
    days_supply IS NULL
    OR trim(days_supply) = ''
    OR cast(days_supply as double) IS NULL
  )

UNION ALL

-- Validation: DAYS_SUPPLY is not numeric (general case)
SELECT 
  'DAYS_SUPPLY contains a non numeric format',
  'DAYS_SUPPLY',
  cast(days_supply as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND days_supply IS NOT NULL
  AND trim(days_supply) <> ''
  AND cast(days_supply as double) IS NULL

UNION ALL

-- Validation: RX_DATE must be non-blank and in YYYYMMDD format when status is Active
SELECT
  'RX_DATE contains blank or NULL values or not in YYYYMMDD format',
  'RX_DATE',
  cast(t1.rx_date as string),
  cast(t1.sp_patient_id as string),
  cast(t1.transaction_id as string),
  t1.date_created,
  t1.id
FROM silver.curated_prx_patient_status t1
LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id
LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id
WHERE 
  t1.is_processed = true
  AND s1.status = 'Active'
  AND (
    rx_date IS NULL
    OR trim(rx_date) = ''
    OR to_date(rx_date, 'yyyyMMdd') IS NULL
  )

UNION ALL

-- Validation: HIPAA_AUTH_DATE must follow rules based on HIPAA_AUTH_STATUS
SELECT 
  'HIPAA_AUTH_DATE must be in YYYYMMDD format and not blank when HIPAA_AUTH_STATUS = Y; must be null when HIPAA_AUTH_STATUS = N',
  'HIPAA_AUTH_DATE',
  cast(hipaa_auth_date as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE 
  (
    hipaa_auth_status = 'Y'
    AND (
      hipaa_auth_date IS NULL
      OR trim(hipaa_auth_date) = ''
      OR to_date(hipaa_auth_date, 'yyyyMMdd') IS NULL
      OR length(hipaa_auth_date) <> 8
    )
  )
  OR (
    hipaa_auth_status = 'N'
    AND hipaa_auth_date IS NOT NULL
    AND trim(hipaa_auth_date) <> ''
  )

UNION ALL

-- HIPAA_AUTH_STATUS: Null, blank, or invalid format
SELECT
  'HIPAA_AUTH_STATUS contains blank or NULL values or not in Y, N, U format',
  'HIPAA_AUTH_STATUS',
  cast(hipaa_auth_status as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    hipaa_auth_status IS NULL 
    OR trim(hipaa_auth_status) = '' 
    OR hipaa_auth_status NOT IN ('N', 'Y', 'U')
  )

UNION ALL

-- LATEST_INDICATION: Null, blank, or not in expected values
SELECT 
  'LATEST_INDICATION contains blank or NULL values or not in WHIM format',
  'LATEST_INDICATION',
  cast(latest_indication as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    latest_indication IS NULL 
    OR trim(latest_indication) = '' 
    OR latest_indication NOT IN ('WHIM', 'Other')
  )

UNION ALL

-- MARKETING_CONSENT_STATUS: Null, blank, or invalid
SELECT 
  'MARKETING_CONSENT_STATUS contains blank or NULL values or not in Y, N, U format',
  'MARKETING_CONSENT_STATUS',
  cast(marketing_consent_status as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    marketing_consent_status IS NULL 
    OR trim(marketing_consent_status) = '' 
    OR marketing_consent_status NOT IN ('N', 'Y', 'U')
  )

UNION ALL

-- PATIENT_AGE: Null, blank, not integer, or <= 0
SELECT 
  'PATIENT_AGE contains blank or NULL values, non-integer values, or values not greater than 0',
  'PATIENT_AGE',
  cast(patient_age as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    patient_age IS NULL
    OR trim(patient_age) = ''
    OR cast(patient_age as int) IS NULL
    OR cast(patient_age as int) <= 0
  )

UNION ALL

-- PRESCRIBER_DEA: Invalid character format
SELECT 
  'value not in correct varchar format',
  'PRESCRIBER_DEA',
  cast(prescriber_dea as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND prescriber_dea IS NOT NULL
  AND NOT regexp_like(prescriber_dea, '^[a-zA-Z0-9]+$')

UNION ALL

-- PRESCRIBER_DEA should be present if NPI is missing
SELECT 
  'prescriber_dea should be present when npi is not available',
  'PRESCRIBER_DEA',
  cast(prescriber_dea as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND prescriber_dea IS NULL
  AND prescriber_npi IS NULL

UNION ALL
-- PRIMARY_COVERAGE_TYPE validation
SELECT 
  'PRIMARY_COVERAGE_TYPE contains blank or NULL values or not in M,P,A,C format',
  'PRIMARY_COVERAGE_TYPE',
  cast(primary_coverage_type as string),
  cast(t1.sp_patient_id as string),
  cast(t1.transaction_id as string),
  t1.date_created,
  t1.id
FROM silver.curated_prx_patient_status t1
LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id
LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id
WHERE 
  t1.is_processed = true
  AND s1.status <> 'Pending'
  AND (
    primary_coverage_type IS NULL
    OR trim(primary_coverage_type) = ''
    OR primary_coverage_type NOT IN ('M', 'P', 'A', 'C')
    OR length(primary_coverage_type) != 1
  )

UNION ALL

-- QUANTITY_DISPENSED must be a valid number
SELECT 
  'value not in valid Format of Number',
  'QUANTITY_DISPENSED',
  cast(quantity_dispensed as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND quantity_dispensed IS NOT NULL
  AND cast(quantity_dispensed as int) IS NULL

UNION ALL

-- QUANTITY_DISPENSED required when ACTIVE/SHIPMENT
SELECT 
  'QUANTITY_DISPENSED contains blank or NULL values when status/substatus combination is ACTIVE/SHIPMENT',
  'QUANTITY_DISPENSED',
  cast(quantity_dispensed as string),
  cast(t2.sp_patient_id as string),
  cast(t2.transaction_id as string),
  t2.date_created,
  t2.id
FROM silver.curated_prx_patient_status t2
LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
WHERE 
  t2.is_processed = true
  AND s3.status = 'Active'
  AND s4.sub_status = 'SHIPMENT'
  AND (
    quantity_dispensed IS NULL
    OR trim(quantity_dispensed) = ''
  )

UNION ALL

-- TRANSACTION_ID: Null, non-integer, or duplicate with TRANSACTION_SEQUENCE
SELECT 
  'TRANSACTION_ID contains blank/NULL, non-integer value, or duplicate combination with Transaction_Sequence',
  'TRANSACTION_ID',
  cast(transaction_id as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM (
  SELECT 
    transaction_id,
    transaction_sequence,
    sp_patient_id,
    date_created,
    id,
    COUNT(*) OVER (PARTITION BY transaction_id, transaction_sequence) AS txn_count
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true
) tmp
WHERE 
  transaction_id IS NULL
  OR trim(transaction_id) = ''
  OR cast(transaction_id as int) IS NULL
  OR txn_count > 1

UNION ALL

-- TRANSACTION_SEQUENCE: blank, non-integer, or incorrect increment
SELECT 
  'TRANSACTION_SEQUENCE contains blank, non-integer, or not sequentially incremented values as required',
  'TRANSACTION_SEQUENCE',
  cast(transaction_sequence as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM (
  SELECT 
    *,
    row_number() OVER (PARTITION BY transaction_id ORDER BY date_created) - 1 AS expected_seq,
    cast(transaction_sequence as int) AS ts_int
  FROM silver.curated_prx_patient_status
  WHERE is_processed = true
) tmp
WHERE 
  transaction_sequence IS NULL
  OR trim(transaction_sequence) = ''
  OR ts_int IS NULL
  OR ts_int <> expected_seq

UNION ALL

-- Validation: First record must have status 'Pending' and sub_status 'NEW'
SELECT 
  'First record should have status Pending and sub_staus as New',
  'status, sub_status',
  cast(concat(status, sub_status) as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM (
  SELECT 
    sp_patient_id,
    t1.date_created,
    t1.id,
    s1.status,
    s2.sub_status,
    t1.status_date,
    transaction_id,
    row_number() OVER (PARTITION BY t1.sp_patient_id ORDER BY t1.status_date) AS rn
  FROM silver.curated_prx_patient_status t1
  LEFT JOIN dwh_utils.status_catalog s1 ON t1.status = s1.id
  LEFT JOIN dwh_utils.sub_status_catalog s2 ON t1.sub_status = s2.id
  WHERE t1.is_processed = true
) first_record
WHERE rn = 1 
  AND (
    status <> 'Pending' 
    OR sub_status <> 'NEW'
  )

UNION ALL

-- Validation: TRANSACTION_TYPE should be one of allowed values
SELECT 
  'TRANSACTION_TYPE contains blank or NULL values or not in COM, QUICK START, PAP, BRIDGE',
  'TRANSACTION_TYPE',
  cast(transaction_type as string),
  cast(sp_patient_id as string),
  cast(transaction_id as string),
  date_created,
  id
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND (
    transaction_type IS NULL
    OR trim(transaction_type) = ''
    OR transaction_type NOT IN ('COM', 'QUICK START', 'PAP', 'BRIDGE')
  )
)

UNION ALL

SELECT 
  'PHI field should not be populated when HIPAA_AUTH_STATUS is N' AS error_message,
  'phi_field_violation' AS error_type,
  CASE 
    WHEN SHIP_TRACKING_NO IS NOT NULL AND trim(SHIP_TRACKING_NO) != '' THEN concat('SHIP_TRACKING_NO: ', cast(SHIP_TRACKING_NO as string))
    WHEN PATIENT_PHONE IS NOT NULL AND trim(PATIENT_PHONE) != '' THEN concat('PATIENT_PHONE: ', cast(PATIENT_PHONE as string))
    WHEN PATIENT_LAST_NAME IS NOT NULL AND trim(PATIENT_LAST_NAME) != '' THEN concat('PATIENT_LAST_NAME: ', cast(PATIENT_LAST_NAME as string))
    WHEN PATIENT_FIRST_NAME IS NOT NULL AND trim(PATIENT_FIRST_NAME) != '' THEN concat('PATIENT_FIRST_NAME: ', cast(PATIENT_FIRST_NAME as string))
    WHEN PATIENT_EMAIL IS NOT NULL AND trim(PATIENT_EMAIL) != '' THEN concat('PATIENT_EMAIL: ', cast(PATIENT_EMAIL as string))
    WHEN PATIENT_CITY IS NOT NULL AND trim(PATIENT_CITY) != '' THEN concat('PATIENT_CITY: ', cast(PATIENT_CITY as string))
    WHEN PATIENT_ALT_PHONE IS NOT NULL AND trim(PATIENT_ALT_PHONE) != '' THEN concat('PATIENT_ALT_PHONE: ', cast(PATIENT_ALT_PHONE as string))
    WHEN PATIENT_ADDRESS_LINE2 IS NOT NULL AND trim(PATIENT_ADDRESS_LINE2) != '' THEN concat('PATIENT_ADDRESS_LINE2: ', cast(PATIENT_ADDRESS_LINE2 as string))
    WHEN PATIENT_ADDRESS_LINE1 IS NOT NULL AND trim(PATIENT_ADDRESS_LINE1) != '' THEN concat('PATIENT_ADDRESS_LINE1: ', cast(PATIENT_ADDRESS_LINE1 as string))
    WHEN HUB_PATIENT_ID IS NOT NULL AND trim(HUB_PATIENT_ID) != '' THEN concat('HUB_PATIENT_ID: ', cast(HUB_PATIENT_ID as string))
    WHEN COPAY_ASSIST_ID IS NOT NULL AND trim(COPAY_ASSIST_ID) != '' THEN concat('COPAY_ASSIST_ID: ', cast(COPAY_ASSIST_ID as string))
    WHEN CAREGIVER_PHONE IS NOT NULL AND trim(CAREGIVER_PHONE) != '' THEN concat('CAREGIVER_PHONE: ', cast(CAREGIVER_PHONE as string))
    WHEN CAREGIVER_NAME IS NOT NULL AND trim(CAREGIVER_NAME) != '' THEN concat('CAREGIVER_NAME: ', cast(CAREGIVER_NAME as string))
    WHEN CAREGIVER_EMAIL IS NOT NULL AND trim(CAREGIVER_EMAIL) != '' THEN concat('CAREGIVER_EMAIL: ', cast(CAREGIVER_EMAIL as string))
    ELSE 'Unknown column' 
  END AS column_value,
  cast(sp_patient_id as string) AS sp_patient_id,
  cast(transaction_id as string) AS transaction_id,
  cast(date_created as string) AS date_created,
  cast(id as string) AS id,
  'pantheRx' AS source,
  'yes' AS is_critical,
  current_timestamp() AS error_timestamp
FROM silver.curated_prx_patient_status
WHERE hipaa_auth_status = 'N'
  AND (
    trim(SHIP_TRACKING_NO) != '' 
    OR trim(PATIENT_PHONE) != ''
    OR trim(PATIENT_LAST_NAME) != ''
    OR trim(PATIENT_FIRST_NAME) != ''
    OR trim(PATIENT_EMAIL) != ''
    OR trim(PATIENT_CITY) != ''
    OR trim(PATIENT_ALT_PHONE) != ''
    OR trim(PATIENT_ADDRESS_LINE2) != ''
    OR trim(PATIENT_ADDRESS_LINE1) != ''
    OR trim(HUB_PATIENT_ID) != ''
    OR trim(COPAY_ASSIST_ID) != ''
    OR trim(CAREGIVER_PHONE) != ''
    OR trim(CAREGIVER_NAME) != ''
    OR trim(CAREGIVER_EMAIL) != ''
  )

UNION ALL

SELECT 
  error_message,
  concat(error_column, ' Validation'),
  column_value, 
  sp_id as sp_patient_id, 
  t_id as transaction_id,
  date_id as DATE_CREATED,
  prxid as ID,
  'pantheRx', 
  'no', 
  current_timestamp()
FROM (   

  -- CTE: flattened PRODUCT_LOT
  WITH flattened_product_lots AS (
    SELECT 
      t2.product_lot,
      lot_value,
      s3.status,
      s4.sub_status,
      t2.sp_patient_id,
      t2.transaction_id,
      t2.date_created,
      t2.id
    FROM silver.curated_prx_patient_status t2
    LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
    LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
    LATERAL VIEW explode(split(t2.product_lot, ';')) AS lot_value
    WHERE t2.is_processed = true
      AND s3.status = 'Active'
      AND s4.sub_status = 'SHIPMENT'
  ),

  -- CTE: flattened PRODUCT_EXP_DATE
  flattened_product_exp_date AS (
    SELECT 
      t2.product_exp_date,
      lot_value,
      s3.status,
      s4.sub_status,
      t2.sp_patient_id,
      t2.transaction_id,
      t2.date_created,
      t2.id
    FROM silver.curated_prx_patient_status t2
    LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
    LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
    LATERAL VIEW explode(split(t2.product_exp_date, ';')) AS lot_value
    WHERE t2.is_processed = true
      AND s3.status = 'Active'
      AND s4.sub_status = 'SHIPMENT'
  )

  -- Validation 1: PRODUCT_LOT - non-blank and valid format
  SELECT 
    'PRODUCT_LOT must be available in valid Format and not blank when the status/substatus combination is ACTIVE/SHIPMENT' AS error_message,
    'PRODUCT_LOT' AS error_column,
    cast(product_lot as string) AS column_value,
    cast(sp_patient_id as string) AS sp_id,
    cast(transaction_id as string) AS t_id,
    date_created AS date_id,
    id AS prxid
  FROM flattened_product_lots
  WHERE 
    product_lot IS NULL 
    OR trim(product_lot) = '' 
    OR (lot_value IS NOT NULL AND NOT regexp_like(lot_value, '^[a-zA-Z0-9]+$'))

  UNION ALL

  -- Validation 2: PRODUCT_EXP_DATE - non-blank and correct date format
  SELECT 
    'product_exp_date must be available in valid Format and not blank when the status/substatus combination is ACTIVE/SHIPMENT' AS error_message,
    'product_exp_date' AS error_column,
    cast(product_exp_date as string) AS column_value,
    cast(sp_patient_id as string) AS sp_id,
    cast(transaction_id as string) AS t_id,
    date_created AS date_id,
    id AS prxid
  FROM flattened_product_exp_date
  WHERE 
    product_exp_date IS NULL 
    OR trim(product_exp_date) = ''
    OR length(product_exp_date) <> 8 
    OR to_date(product_exp_date, 'yyyyMMdd') IS NULL

  UNION ALL

  -- Validation 3: PRODUCT_LOT blank check (non-split)
  SELECT 
    'PRODUCT_LOT contains blank or NULL values when status/substatus combination is ACTIVE/SHIPMENT',
    'PRODUCT_LOT',
    cast(product_lot as string),
    cast(t2.sp_patient_id as string),
    cast(t2.transaction_id as string),
    t2.date_created,
    t2.id
  FROM silver.curated_prx_patient_status t2
  LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
  LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
  WHERE 
    t2.is_processed = true 
    AND s3.status = 'Active' 
    AND s4.sub_status = 'SHIPMENT' 
    AND (product_lot IS NULL OR trim(product_lot) = '')

  UNION ALL

  -- Validation 4: PRODUCT_EXP_DATE blank check (non-split)
  SELECT 
    'product_exp_date contains blank or NULL values when status/substatus combination is ACTIVE/SHIPMENT',
    'product_exp_date',
    cast(product_exp_date as string),
    cast(t2.sp_patient_id as string),
    cast(t2.transaction_id as string),
    t2.date_created,
    t2.id
  FROM silver.curated_prx_patient_status t2
  LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
  LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
  WHERE 
    t2.is_processed = true 
    AND s3.status = 'Active' 
    AND s4.sub_status = 'SHIPMENT' 
    AND (product_exp_date IS NULL OR trim(product_exp_date) = '')

  UNION ALL

  SELECT 'CAREGIVER_OK_FOR_MESSAGE contains blank or NULL values or not in Y, N, U format',
       'CAREGIVER_OK_FOR_MESSAGE',
       cast(CAREGIVER_OK_FOR_MESSAGE as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created as date_id,
       id as prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    CAREGIVER_OK_FOR_MESSAGE IS NULL OR trim(CAREGIVER_OK_FOR_MESSAGE) = ''
    OR CAREGIVER_OK_FOR_MESSAGE NOT IN ('N','Y','U')
  )

UNION ALL

SELECT 'CLAIM_TYPE contains blank or NULL values or not in M,P,A,C format',
       'CLAIM_TYPE',
       cast(CLAIM_TYPE as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    CLAIM_TYPE IS NULL OR trim(CLAIM_TYPE) = ''
    OR CLAIM_TYPE NOT IN ('M', 'P', 'A', 'C')
    OR length(CLAIM_TYPE) != 1
  )

UNION ALL

SELECT 'DAW contains blank or NULL values or not in Number format',
       'DAW',
       cast(DAW as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    DAW IS NULL OR trim(DAW) = ''
    OR try_cast(DAW as double) IS NULL
  )

UNION ALL

SELECT 'FAMILY_HISTORY_OF_WHIM contains blank or NULL values or not in Y, N, U format',
       'FAMILY_HISTORY_OF_WHIM',
       cast(FAMILY_HISTORY_OF_WHIM as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    FAMILY_HISTORY_OF_WHIM IS NULL OR trim(FAMILY_HISTORY_OF_WHIM) = ''
    OR FAMILY_HISTORY_OF_WHIM NOT IN ('N','Y','U')
  )

UNION ALL

SELECT 'PATIENT_EAP contains blank or NULL values or not in Y, N, U format',
       'PATIENT_EAP',
       cast(PATIENT_EAP as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    PATIENT_EAP IS NULL OR trim(PATIENT_EAP) = ''
    OR PATIENT_EAP NOT IN ('N','Y','U')
  )

UNION ALL

SELECT 'PATIENT_GENDER contains blank or NULL values or not in M, F,Unknown/Other format',
       'PATIENT_GENDER',
       cast(PATIENT_GENDER as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    PATIENT_GENDER IS NULL OR trim(PATIENT_GENDER) = ''
    OR PATIENT_GENDER NOT IN ('M', 'F', 'U')
  )

UNION ALL

SELECT 'PATIENT_OK_FOR_MESSAGE contains blank or NULL values or not in Y, N, U format',
       'PATIENT_OK_FOR_MESSAGE',
       cast(PATIENT_OK_FOR_MESSAGE as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    PATIENT_OK_FOR_MESSAGE IS NULL OR trim(PATIENT_OK_FOR_MESSAGE) = ''
    OR PATIENT_OK_FOR_MESSAGE NOT IN ('Y', 'N', 'U')
  )

UNION ALL

SELECT 'PATIENT_STATE contains blank or NULL values',
       'PATIENT_STATE',
       cast(PATIENT_STATE as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    PATIENT_STATE IS NULL OR trim(PATIENT_STATE) = ''
  )

UNION ALL

SELECT 'PHARMACY_CODE contains blank or NULL values',
       'PHARMACY_CODE',
       cast(PHARMACY_CODE as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    PHARMACY_CODE IS NULL OR trim(PHARMACY_CODE) = ''
  )

UNION ALL

SELECT 'PHARMACY_NPI contains blank or NULL values or not in valid format',
       'PHARMACY_NPI',
       cast(PHARMACY_NPI as string),
       cast(sp_patient_id as string),
       cast(transaction_id as string),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    PHARMACY_NPI IS NULL OR trim(PHARMACY_NPI) = ''
    OR length(PHARMACY_NPI) <> 10
    OR try_cast(PHARMACY_NPI as int) IS NULL
    OR PHARMACY_NPI = '**********'
  )

UNION ALL

SELECT 'PRESCRIBER_ADDRESS_LINE1 contains blank or NULL values',
       'PRESCRIBER_ADDRESS_LINE1',
       CAST(PRESCRIBER_ADDRESS_LINE1 AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created AS date_id,
       id AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRESCRIBER_ADDRESS_LINE1 IS NULL OR TRIM(PRESCRIBER_ADDRESS_LINE1) = '')

UNION ALL

SELECT 'PRESCRIBER_CITY contains blank or NULL values',
       'PRESCRIBER_CITY',
       CAST(PRESCRIBER_CITY AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRESCRIBER_CITY IS NULL OR TRIM(PRESCRIBER_CITY) = '')

UNION ALL

SELECT 'PRESCRIBER_FIRST_NAME contains blank or NULL values',
       'PRESCRIBER_FIRST_NAME',
       CAST(PRESCRIBER_FIRST_NAME AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRESCRIBER_FIRST_NAME IS NULL OR TRIM(PRESCRIBER_FIRST_NAME) = '')

UNION ALL

SELECT 'PRESCRIBER_LAST_NAME contains blank or NULL values',
       'PRESCRIBER_LAST_NAME',
       CAST(PRESCRIBER_LAST_NAME AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRESCRIBER_LAST_NAME IS NULL OR TRIM(PRESCRIBER_LAST_NAME) = '')

UNION ALL

SELECT 'PRESCRIBER_PHONE contains blank or NULL values or not in valid format',
       'PRESCRIBER_PHONE',
       CAST(PRESCRIBER_PHONE AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND (
    PRESCRIBER_PHONE IS NULL OR TRIM(PRESCRIBER_PHONE) = ''
    OR LENGTH(PRESCRIBER_PHONE) <> 10
    OR TRY_CAST(PRESCRIBER_PHONE AS INT) IS NULL
    OR PRESCRIBER_PHONE = '**********'
  )

UNION ALL

SELECT 'PRESCRIBER_SPECIALTY contains blank or NULL values',
       'PRESCRIBER_SPECIALTY',
       CAST(PRESCRIBER_SPECIALTY AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRESCRIBER_SPECIALTY IS NULL OR TRIM(PRESCRIBER_SPECIALTY) = '')

UNION ALL

SELECT 'PRESCRIBER_STATE contains blank or NULL values',
       'PRESCRIBER_STATE',
       CAST(PRESCRIBER_STATE AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRESCRIBER_STATE IS NULL OR TRIM(PRESCRIBER_STATE) = '')

UNION ALL

SELECT 'PRIMARY_PA_REQUIRED contains blank or NULL values or not in Y, N, U format',
       'PRIMARY_PA_REQUIRED',
       CAST(PRIMARY_PA_REQUIRED AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (
  PRIMARY_PA_REQUIRED IS NULL OR TRIM(PRIMARY_PA_REQUIRED) = ''
  OR PRIMARY_PA_REQUIRED NOT IN ('Y', 'N', 'U')
)

UNION ALL

SELECT 'PRIMARY_PBM_BIN contains blank or NULL values or not in number format',
       'PRIMARY_PBM_BIN',
       CAST(PRIMARY_PBM_BIN AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (
  PRIMARY_PBM_BIN IS NULL OR TRIM(PRIMARY_PBM_BIN) = ''
  OR LENGTH(PRIMARY_PBM_BIN) <> 6
  OR TRY_CAST(PRIMARY_PBM_BIN AS INT) IS NULL
)

UNION ALL

SELECT 'PRIMARY_PBM_NAME contains blank or NULL values',
       'PRIMARY_PBM_NAME',
       CAST(PRIMARY_PBM_NAME AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRIMARY_PBM_NAME IS NULL OR TRIM(PRIMARY_PBM_NAME) = '')

UNION ALL

SELECT 'PRIMARY_PBM_NAME should present when the primary coverage type is "Pharmaceutical"',
       'PRIMARY_PBM_NAME',
       CAST(PRIMARY_PBM_NAME AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       date_created,
       id
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND PRIMARY_COVERAGE_TYPE = 'p'
  AND (PRIMARY_PBM_NAME IS NULL OR TRIM(PRIMARY_PBM_NAME) = '')

UNION ALL

SELECT 'PRIMARY_PBM_PLAN_ID contains blank or NULL values','PRIMARY_PBM_PLAN_ID', CAST(PRIMARY_PBM_PLAN_ID AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRIMARY_PBM_PLAN_ID IS NULL OR TRIM(PRIMARY_PBM_PLAN_ID) = '')

UNION ALL

SELECT 'PRODUCT_NAME contains blank or NULL values','PRODUCT_NAME', CAST(PRODUCT_NAME AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PRODUCT_NAME IS NULL OR TRIM(PRODUCT_NAME) = '')

UNION ALL

SELECT 'PROGRAM_NAME contains blank or NULL values','PROGRAM_NAME', CAST(PROGRAM_NAME AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (PROGRAM_NAME IS NULL OR TRIM(PROGRAM_NAME) = '' OR PROGRAM_NAME NOT IN ('Xolremdi'))

UNION ALL

SELECT 'REC_DATE contains blank or NULL values or not in YYYYMMDDHHMMSS','REC_DATE', CAST(REC_DATE AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (REC_DATE IS NULL OR TRIM(REC_DATE) = '' OR LENGTH(REC_DATE) <> 14 OR TO_TIMESTAMP(REC_DATE, 'yyyyMMddHHmmss') IS NULL)

UNION ALL

SELECT 'REFERRAL_SOURCE contains blank or NULL values or not in HUB,DIRECT,PHARM','REFERRAL_SOURCE', CAST(REFERRAL_SOURCE AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (REFERRAL_SOURCE IS NULL OR TRIM(REFERRAL_SOURCE) = '' OR REFERRAL_SOURCE NOT IN ('HUB', 'DIRECT', 'PHARM'))

UNION ALL

SELECT 'REFILLS_REMAINING contains blank or NULL values or not in Number format','REFILLS_REMAINING', CAST(REFILLS_REMAINING AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (REFILLS_REMAINING IS NULL OR TRIM(REFILLS_REMAINING) = '' OR TRY_CAST(REFILLS_REMAINING AS INT) IS NULL)

UNION ALL

SELECT 
  'REFILLS_REMAINING should be (Rx Refills Allowed) - (Rx Fill Number + 1) when status/substatus combination is ACTIVE/SHIPMENT',
  'REFILLS_REMAINING',
  CAST(t2.REFILLS_REMAINING AS STRING),
  CAST(t2.sp_patient_id AS STRING),
  CONCAT(
    CAST(t2.transaction_id AS STRING), 
    ' | rx_refills_allowed = ', 
    CAST(t2.rx_refills_allowed AS STRING), 
    ' | rx_fill_number = ', 
    CAST(t2.rx_fill_number AS STRING)
  ),
  t2.date_created, 
  t2.id
FROM silver.curated_prx_patient_status t2
LEFT JOIN dwh_utils.status_catalog s3 ON t2.status = s3.id
LEFT JOIN dwh_utils.sub_status_catalog s4 ON t2.sub_status = s4.id
WHERE t2.is_processed = true
  AND s3.status = 'Active'
  AND s4.sub_status = 'SHIPMENT'
  AND t2.REFILLS_REMAINING <> (t2.rx_refills_allowed - (t2.rx_fill_number + 1))


UNION ALL

SELECT 'RX_FILL_NUMBER must be less than or equal to the Refills Allowed and should be a valid integer','RX_FILL_NUMBER', CAST(RX_FILL_NUMBER AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (RX_FILL_NUMBER IS NULL OR TRIM(RX_FILL_NUMBER) = '' OR TRY_CAST(RX_FILL_NUMBER AS INT) IS NULL OR TRY_CAST(RX_FILL_NUMBER AS INT) > rx_refills_allowed)

UNION ALL

SELECT 'RX_NUMBER contains blank or NULL values or not in Number format','RX_NUMBER', CAST(RX_NUMBER AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (RX_NUMBER IS NULL OR TRIM(RX_NUMBER) = '' OR TRY_CAST(RX_NUMBER AS INT) IS NULL)

UNION ALL

SELECT 'RX_REFILLS_ALLOWED contains blank or NULL values or not in Number format','RX_REFILLS_ALLOWED', CAST(RX_REFILLS_ALLOWED AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (RX_REFILLS_ALLOWED IS NULL OR TRIM(RX_REFILLS_ALLOWED) = '' OR TRY_CAST(RX_REFILLS_ALLOWED AS INT) IS NULL)

UNION ALL

SELECT 'SECONDARY_PA_REQUIRED contains blank or NULL values or not in Y,N,U format','SECONDARY_PA_REQUIRED', CAST(SECONDARY_PA_REQUIRED AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (SECONDARY_PA_REQUIRED IS NULL OR TRIM(SECONDARY_PA_REQUIRED) = '' OR SECONDARY_PA_REQUIRED NOT IN ('Y', 'N', 'U'))

UNION ALL

SELECT 'TERTIARY_PA_REQUIRED contains blank or NULL values or not in Y,N,U format','TERTIARY_PA_REQUIRED', CAST(TERTIARY_PA_REQUIRED AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (TERTIARY_PA_REQUIRED IS NULL OR TRIM(TERTIARY_PA_REQUIRED) = '' OR TERTIARY_PA_REQUIRED NOT IN ('Y', 'N', 'U'))

UNION ALL

SELECT 'TOTAL_DAILY_DOSE contains blank or NULL values or not in Number Format','TOTAL_DAILY_DOSE', CAST(TOTAL_DAILY_DOSE AS STRING), CAST(sp_patient_id AS STRING), CAST(transaction_id AS STRING), date_created, id
FROM silver.curated_prx_patient_status
WHERE is_processed = true AND (TOTAL_DAILY_DOSE IS NULL OR TRIM(TOTAL_DAILY_DOSE) = '' OR TRY_CAST(TOTAL_DAILY_DOSE AS INT) IS NULL)

UNION ALL

SELECT 'value not in valid email Format' AS error_message,
       'CONTACT_EMAIL' AS error_column,
       CAST(CONTACT_EMAIL AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED ,
       ID 
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND NOT REGEXP_LIKE(CONTACT_EMAIL, '^[a-zA-Z0-9._#%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')

UNION ALL

SELECT 'value not in valid Format' AS error_message,
       'CONTACT_NAME' AS error_column,
       CAST(CONTACT_NAME AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND CONTACT_NAME IS NOT NULL 
  AND NOT REGEXP_LIKE(REPLACE(CONTACT_NAME, ' ', ''), '^[a-zA-Z\\s''\\-\\.]+$')

UNION ALL

SELECT 'value not in valid Format' AS error_message,
       'DIAG_CODE_2' AS error_column,
       CAST(DIAG_CODE_2 AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND NOT REGEXP_LIKE(TRIM(DIAG_CODE_2), '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 'MARKETING_CONSENT_DATE must be in yyyymmmdd format and not blank when MARKETING_CONSENT_STATUS is "Y"' AS error_message,
       'MARKETING_CONSENT_DATE' AS error_column,
       CAST(MARKETING_CONSENT_DATE AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND MARKETING_CONSENT_STATUS = 'Y'
  AND (
        MARKETING_CONSENT_DATE IS NULL 
     OR TRIM(MARKETING_CONSENT_DATE) = '' 
     OR TRY_CAST(MARKETING_CONSENT_DATE AS DATE) IS NULL
     OR LENGTH(MARKETING_CONSENT_DATE) <> 8
  )

UNION ALL

SELECT 'value not in valid Format as 10 digit' AS error_message,
       'PRESCRIBER_FAX' AS error_column,
       CAST(PRESCRIBER_FAX AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND (
        LENGTH(PRESCRIBER_FAX) <> 10 
     OR TRY_CAST(PRESCRIBER_FAX AS INT) IS NULL
  )

UNION ALL

SELECT 'value not in valid yyyymmdd Format' AS error_message,
       'PRIMARY_PA_AUTH_END' AS error_column,
       CAST(PRIMARY_PA_AUTH_END AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND PRIMARY_PA_AUTH_END IS NOT NULL 
  AND (
        TRY_CAST(PRIMARY_PA_AUTH_END AS DATE) IS NULL 
     OR LENGTH(PRIMARY_PA_AUTH_END) <> 8
  )

UNION ALL

SELECT 'value not in valid yyyymmdd Format or may be greater than current date' AS error_message,
       'PRIMARY_PA_AUTH_START' AS error_column,
       CAST(PRIMARY_PA_AUTH_START AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED,
       ID 
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND PRIMARY_PA_AUTH_START IS NOT NULL 
  AND (
        TRY_CAST(PRIMARY_PA_AUTH_START AS DATE) IS NULL 
     OR LENGTH(PRIMARY_PA_AUTH_START) <> 8
     OR TRY_CAST(PRIMARY_PA_AUTH_START AS DATE) > CURRENT_DATE()
  )

UNION ALL

SELECT 'value not in valid format as single char in M,P,A,C format' AS error_message,
       'SECONDARY_COVERAGE_TYPE' AS error_column,
       CAST(SECONDARY_COVERAGE_TYPE AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_COVERAGE_TYPE IS NOT NULL
  AND (
    SECONDARY_COVERAGE_TYPE NOT IN ('M', 'P', 'A', 'C') OR LENGTH(SECONDARY_COVERAGE_TYPE) <> 1
  )

UNION ALL

SELECT 'value not in correct YYYYMMDD format' AS error_message,
       'SECONDARY_PA_AUTH_END' AS error_column,
       CAST(SECONDARY_PA_AUTH_END AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_PA_AUTH_END IS NOT NULL
  AND (
    TO_DATE(SECONDARY_PA_AUTH_END, 'yyyyMMdd') IS NULL
    OR LENGTH(SECONDARY_PA_AUTH_END) <> 8
  )

UNION ALL

SELECT 'value not in correct YYYYMMDD format or may be greater than current date' AS error_message,
       'SECONDARY_PA_AUTH_START' AS error_column,
       CAST(SECONDARY_PA_AUTH_START AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_PA_AUTH_START IS NOT NULL
  AND (
    TO_DATE(SECONDARY_PA_AUTH_START, 'yyyyMMdd') IS NULL
    OR LENGTH(SECONDARY_PA_AUTH_START) <> 8
    OR TO_DATE(SECONDARY_PA_AUTH_START, 'yyyyMMdd') > CURRENT_DATE()
  )

UNION ALL

SELECT 'value not from Commercial, Medicaid, Medicare, Other, PAP, Federal, Tricare, VA, Cash, Uninsured, Copay Assistance' AS error_message,
       'SECONDARY_PAYOR_TYPE' AS error_column,
       CAST(SECONDARY_PAYOR_TYPE AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_PAYOR_TYPE NOT IN (
    'Commercial','Medicaid','Medicare','Other','PAP','Federal','Tricare','VA','Cash','Uninsured','Copay Assistance'
  )

UNION ALL

SELECT 'value not in correct 6 digit format' AS error_message,
       'SECONDARY_PBM_BIN' AS error_column,
       CAST(SECONDARY_PBM_BIN AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND NOT REGEXP_LIKE(SECONDARY_PBM_BIN, '^[0-9]{6}$')

UNION ALL

SELECT 'value not in correct varchar format' AS error_message,
       'SECONDARY_PBM_GROUP' AS error_column,
       CAST(SECONDARY_PBM_GROUP AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_PBM_GROUP IS NOT NULL
  AND NOT REGEXP_LIKE(SECONDARY_PBM_GROUP, '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 'value not in correct varchar format' AS error_message,
       'SECONDARY_PBM_NAME' AS error_column,
       CAST(SECONDARY_PBM_NAME AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_PBM_NAME IS NOT NULL
  AND NOT REGEXP_LIKE(REPLACE(SECONDARY_PBM_NAME, ' ', ''), '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 'value not in correct varchar format' AS error_message,
       'SECONDARY_PBM_PCN' AS error_column,
       CAST(SECONDARY_PBM_PCN AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_PBM_PCN IS NOT NULL
  AND NOT REGEXP_LIKE(SECONDARY_PBM_PCN, '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 'value not in correct varchar format' AS error_message,
       'SECONDARY_PBM_PLAN_ID' AS error_column,
       CAST(SECONDARY_PBM_PLAN_ID AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SECONDARY_PBM_PLAN_ID IS NOT NULL
  AND NOT REGEXP_LIKE(REPLACE(SECONDARY_PBM_PLAN_ID, ' ', ''), '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 'value not in correct varchar format' AS error_message,
       'SHIP_CARRIER' AS error_column,
       CAST(SHIP_CARRIER AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SHIP_CARRIER IS NOT NULL
  AND NOT REGEXP_LIKE(SHIP_CARRIER, '^[a-zA-Z]+$')

UNION ALL

SELECT 'value not from given Home, Office, Other values' AS error_message,
       'SHIP_TO_LOCATION' AS error_column,
       CAST(SHIP_TO_LOCATION AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND SHIP_TO_LOCATION IS NOT NULL
  AND SHIP_TO_LOCATION NOT IN ('Home', 'Office', 'Other')

UNION ALL

SELECT 'value not from M, P, A, C as single character' AS error_message,
       'TERTIARY_COVERAGE_TYPE' AS error_column,
       CAST(TERTIARY_COVERAGE_TYPE AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND TERTIARY_COVERAGE_TYPE IS NOT NULL
  AND (TERTIARY_COVERAGE_TYPE NOT IN ('M', 'P', 'A', 'C') OR LENGTH(TERTIARY_COVERAGE_TYPE) <> 1)

UNION ALL

SELECT 'value not in correct date format' AS error_message,
       'TERTIARY_PA_AUTH_END' AS error_column,
       CAST(TERTIARY_PA_AUTH_END AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND TERTIARY_PA_AUTH_END IS NOT NULL
  AND (
    TO_DATE(TERTIARY_PA_AUTH_END, 'yyyyMMdd') IS NULL OR LENGTH(TERTIARY_PA_AUTH_END) <> 8
  )

UNION ALL

SELECT 'value not in correct date format or may be greater than current date' AS error_message,
       'TERTIARY_PA_AUTH_START' AS error_column,
       CAST(TERTIARY_PA_AUTH_START AS STRING),
       CAST(sp_patient_id AS STRING),
       CAST(transaction_id AS STRING),
       DATE_CREATED AS date_id,
       ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true
  AND TERTIARY_PA_AUTH_START IS NOT NULL
  AND (
    TO_DATE(TERTIARY_PA_AUTH_START, 'yyyyMMdd') IS NULL
    OR LENGTH(TERTIARY_PA_AUTH_START) <> 8
    OR TO_DATE(TERTIARY_PA_AUTH_START, 'yyyyMMdd') > CURRENT_DATE()
  )

UNION ALL

SELECT 
  'value not from Commercial Medicaid,Medicare,Other,PAP,Federal,Tricare,VA,Cash,Uninsured,Copay Assistance',
  'tertiary_payor_type',
  CAST(tertiary_payor_type AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND tertiary_payor_type NOT IN (
    'Commercial','Medicaid','Medicare','Other','PAP','Federal','Tricare','VA','Cash','Uninsured','Copay Assistance'
  )

UNION ALL

SELECT 
  'value not in correct 6 digit format',
  'TERTIARY_PBM_BIN',
  CAST(TERTIARY_PBM_BIN AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND NOT REGEXP_LIKE(TERTIARY_PBM_BIN, '^\\d{6}$')

UNION ALL

SELECT 
  'value not in correct varchar format',
  'TERTIARY_PBM_GROUP',
  CAST(TERTIARY_PBM_GROUP AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND TERTIARY_PBM_GROUP IS NOT NULL 
  AND NOT REGEXP_LIKE(TERTIARY_PBM_GROUP, '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 
  'value not in correct varchar format',
  'TERTIARY_PBM_NAME',
  CAST(TERTIARY_PBM_NAME AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND TERTIARY_PBM_NAME IS NOT NULL 
  AND NOT REGEXP_LIKE(REPLACE(TERTIARY_PBM_NAME, ' ', ''), '^[a-zA-Z0-9-]+$')

UNION ALL

SELECT 
  'value not in correct varchar format',
  'TERTIARY_PBM_PCN',
  CAST(TERTIARY_PBM_PCN AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND TERTIARY_PBM_PCN IS NOT NULL 
  AND NOT REGEXP_LIKE(TERTIARY_PBM_PCN, '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 
  'value not in correct varchar format',
  'TERTIARY_PBM_PLAN_ID',
  CAST(TERTIARY_PBM_PLAN_ID AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND TERTIARY_PBM_PLAN_ID IS NOT NULL 
  AND NOT REGEXP_LIKE(REPLACE(TERTIARY_PBM_PLAN_ID, ' ', ''), '^[a-zA-Z0-9-]+$')

UNION ALL

SELECT 
  'value not in correct varchar format or UNIT_OF_MEASURE not EA when status = Active',
  'UNIT_OF_MEASURE',
  CAST(t1.UNIT_OF_MEASURE AS STRING),
  CAST(t1.sp_patient_id AS STRING),
  CAST(t1.transaction_id AS STRING),
  t1.DATE_CREATED AS date_id,
  t1.ID AS prxid
FROM silver.curated_prx_patient_status t1
LEFT JOIN dwh_utils.status_catalog s1 
  ON t1.status = s1.id
WHERE t1.is_processed = true
  AND (
    (UNIT_OF_MEASURE IS NOT NULL AND NOT REGEXP_LIKE(UNIT_OF_MEASURE, '^[A-Za-z]+$'))
    OR (s1.status = 'Active' AND UNIT_OF_MEASURE <> 'EA')
  )

UNION ALL

SELECT 
  'value not in correct varchar format',
  'PRIMARY_PBM_GROUP',
  CAST(PRIMARY_PBM_GROUP AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND PRIMARY_PBM_GROUP IS NOT NULL 
  AND NOT REGEXP_LIKE(PRIMARY_PBM_GROUP, '^[a-zA-Z0-9-]+$')

UNION ALL

SELECT 
  'value not in correct varchar format',
  'PRIMARY_PBM_PCN',
  CAST(PRIMARY_PBM_PCN AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND PRIMARY_PBM_PCN IS NOT NULL 
  AND NOT REGEXP_LIKE(PRIMARY_PBM_PCN, '^[a-zA-Z0-9]+$')

UNION ALL

SELECT 
  'Patient Weight should not be 0',
  'PATIENT_WEIGHT_KG',
  CAST(PATIENT_WEIGHT_KG AS STRING),
  CAST(sp_patient_id AS STRING),
  CAST(transaction_id AS STRING),
  DATE_CREATED AS date_id,
  ID AS prxid
FROM silver.curated_prx_patient_status
WHERE is_processed = true 
  AND PATIENT_WEIGHT_KG = 0

)
;

