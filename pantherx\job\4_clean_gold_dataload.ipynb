{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "928dacb8-29d7-4f27-936c-141b8ffb0a37", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "INSERT INTO gold.prx_patient_status (\n", "  REC_<PERSON>AT<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CODE,PHARMACY_NPI,TRANSACTION_TYPE,TRANSACTION_ID,TRANSACTION_SEQUENCE,PROGRAM_NAME,\n", "  PATIENT_FIRST_NAME, PATIENT_LAST_NAME, REFERRAL_SOURCE, REFERRAL_DATE, HUB_PATIENT_ID, SP_PATIENT_ID,\n", "  PATIENT_DOB, PATIENT_<PERSON>NDER, PATIENT_WEIGHT_KG, PATIENT_ADDRESS_LINE1, PATIENT_ADDRESS_LINE2, PATIENT_CITY,\n", "  PATIENT_STATE, PATIENT_ZIP, PATIENT_PHONE, PATIENT_ALT_PHONE, PATIENT_EMAIL, PATIENT_OK_FOR_MESSAGE, CAREGIVER_NAME,\n", "  CAREGIVER_PHON<PERSON>, CAREGIVER_EMAIL, CAREGIVER_OK_FOR_MESSAGE, PATIENT_EAP, DIAG_CODE_1, DIAG_CODE_2,\n", "  GENETIC_TEST_COMPLETED, GENETIC_TEST_RESULT, FAMILY_HISTORY_OF_WHIM, CURRENT_THERAPIES, PREVIOUS_THERAPIES,\n", "  STATUS_DATE, PRO<PERSON><PERSON>_CONSENT, PROGRAM_CONSENT_DATE, HIPAA_AUTH_STATUS, HIPAA_AUTH_DATE, MARKETING_CONSENT_STATUS,\n", "  MARKETING_CONSENT_DATE, PRESCRIBER_LAST_NAME, PRESCRIBER_FIRST_NAME, PRESCRIBER_ADDRESS_LINE1, PRESCRIBER_ADDRESS_LINE2, PRESCRIBER_CITY, PRESCRIBER_STATE, PRESCRIBER_ZIP, PRESCRIBER_PHONE, PRESCRIBER_FAX,\n", "  PRESCRIBER_NPI, PRESCRIBER_DEA, PRESCRIBER_SPECIALTY, FACILITY_NAME, CONTACT_NAME, CONTACT_EMAIL,\n", "  HCP_SIGNATURE_ON_NEW_RX, RX_DATE, RX_NUMBER, RX_REFILLS_ALLOWED, RX_FILL_NUMBER, REFILLS_REMAINING, DAW, NDC,\n", "  PRODUCT_NAME, <PERSON>U<PERSON><PERSON>TY_DISPENSED, TOTAL_DAILY_DOSE, UNIT_OF_MEASURE, DAYS_SUPPLY, DATE_SHIPPED, PRODUCT_LOT,\n", "  PRODUCT_EXP_DATE,SHIP_TO_LOCATION,SHIP_CARRIER,SHIP_TRACKING_NO,PRIMARY_COVERAGE_TYPE,PRIMARY_PAYOR_NAME,\n", "  PRIMARY_PAYOR_SEGMENT, PRIMARY_PA_REQUIRED, PRIMARY_PA_AUTH_START, PRIMARY_PA_AUTH_END, PRIMARY_PBM_NAME,\n", "  PRIMARY_P<PERSON>_<PERSON>IN, PRIMAR<PERSON>_PBM_PCN, PR<PERSON>AR<PERSON>_<PERSON><PERSON>_GROUP, PRIMARY_PBM_PLAN_ID, SECONDARY_COVERAGE_TYPE, SECONDARY_PAYOR_NAME, SECONDARY_PAYOR_TYPE, SECONDARY_PA_REQUIRED, SECONDARY_PA_AUTH_START, SECONDARY_PA_AUTH_END,\n", "  SECONDARY_PBM_NAME, SECONDARY_PBM_BIN, SECONDARY_PBM_PCN, SECONDARY_PBM_GROUP, SECONDARY_PBM_PLAN_ID,\n", "  TERTIARY_COVERA<PERSON>_TYPE, TERTIARY_PAYOR_NAME, TERTIARY_PAYOR_TYPE, TERTIARY_PA_REQUIRED, TERTIARY_PA_AUTH_START,\n", "  TERTIARY_PA_AUTH_END, TERTIARY_PBM_NAME, TERTIARY_PBM_BIN, TERTIARY_PBM_PCN, TERTIARY_PBM_GROUP, TERTIARY_PBM_PLAN_ID,\n", "  CLAIM_TYPE, PATIENT_COPAY_AMT, COPAY_ASSIST_FLAG, COPAY_ASSIST_ID, COPAY_ASSIST_AMT, PATIENT_FINAL_OOP, DATASOURCE_ID,\n", "  REC_DATE_FORMATTED, REFERRAL_DATE_FORMATTED, STATUS_DATE_FORMATTED, PROGRAM_CONSENT_DATE_FORMATTED,\n", "  HIPAA_AUTH_DATE_FORMATTED, MARKETING_CONSENT_DATE_FORMATTED, RX_DATE_FORMATTED, DATE_SHIPPED_FORMATTED,\n", "  PRODUCT_EXP_DATE_FORMATTED, PRIMARY_PA_AUTH_START_FORMATTED, PRIMARY_PA_AUTH_END_FORMATTED,\n", "  SECONDARY_PA_AUTH_START_FORMATTED, SECONDARY_PA_AUTH_END_FORMATTED, TERTIARY_PA_AUTH_START_FORMATTED,\n", "  TERTIARY_PA_AUTH_END_FORMATTED,PATIENT_AGE,LATEST_INDICATION,PATIENT_AGE_ON_RX\n", ")\n", "SELECT \n", "  REC_<PERSON>AT<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CODE,PHARMACY_NPI,TRANSACTION_TYPE,TRANSACTION_ID,TRANSACTION_SEQUENCE,PROGRAM_NAME,\n", "  PATIENT_FIRST_NAME, PATIENT_LAST_NAME, REFERRAL_SOURCE, REFERRAL_DATE, HUB_PATIENT_ID, SP_PATIENT_ID,\n", "  PATIENT_DOB, PATIENT_<PERSON>NDER, PATIENT_WEIGHT_KG, PATIENT_ADDRESS_LINE1, PATIENT_ADDRESS_LINE2, PATIENT_CITY,\n", "  PATIENT_STATE, PATIENT_ZIP, PATIENT_PHONE, PATIENT_ALT_PHONE, PATIENT_EMAIL, PATIENT_OK_FOR_MESSAGE, CAREGIVER_NAME,\n", "  CAREGIVER_PHON<PERSON>, CAREGIVER_EMAIL, CAREGIVER_OK_FOR_MESSAGE, PATIENT_EAP, DIAG_CODE_1, DIAG_CODE_2,\n", "  GENETIC_TEST_COMPLETED, GENETIC_TEST_RESULT, FAMILY_HISTORY_OF_WHIM, CURRENT_THERAPIES, PREVIOUS_THERAPIES,\n", "  STATUS_DATE, PRO<PERSON><PERSON>_CONSENT, PROGRAM_CONSENT_DATE, HIPAA_AUTH_STATUS, HIPAA_AUTH_DATE, MARKETING_CONSENT_STATUS,\n", "  MARKETING_CONSENT_DATE, PRESCRIBER_LAST_NAME, PRESCRIBER_FIRST_NAME, PRESCRIBER_ADDRESS_LINE1, PRESCRIBER_ADDRESS_LINE2, PRESCRIBER_CITY, PRESCRIBER_STATE, PRESCRIBER_ZIP, PRESCRIBER_PHONE, PRESCRIBER_FAX,\n", "  PRESCRIBER_NPI, PRESCRIBER_DEA, PRESCRIBER_SPECIALTY, FACILITY_NAME, CONTACT_NAME, CONTACT_EMAIL,\n", "  HCP_SIGNATURE_ON_NEW_RX, RX_DATE, RX_NUMBER, RX_REFILLS_ALLOWED, RX_FILL_NUMBER, REFILLS_REMAINING, DAW, NDC,\n", "  PRODUCT_NAME, <PERSON>U<PERSON><PERSON>TY_DISPENSED, TOTAL_DAILY_DOSE, UNIT_OF_MEASURE, DAYS_SUPPLY, DATE_SHIPPED, PRODUCT_LOT,\n", "  PRODUCT_EXP_DATE,SHIP_TO_LOCATION,SHIP_CARRIER,SHIP_TRACKING_NO,PRIMARY_COVERAGE_TYPE,PRIMARY_PAYOR_NAME,\n", "  PRIMARY_PAYOR_SEGMENT, PRIMARY_PA_REQUIRED, PRIMARY_PA_AUTH_START, PRIMARY_PA_AUTH_END, PRIMARY_PBM_NAME,\n", "  PRIMARY_P<PERSON>_<PERSON>IN, PRIMAR<PERSON>_PBM_PCN, PR<PERSON>AR<PERSON>_<PERSON><PERSON>_GROUP, PRIMARY_PBM_PLAN_ID, SECONDARY_COVERAGE_TYPE, SECONDARY_PAYOR_NAME, SECONDARY_PAYOR_TYPE, SECONDARY_PA_REQUIRED, SECONDARY_PA_AUTH_START, SECONDARY_PA_AUTH_END,\n", "  SECONDARY_PBM_NAME, SECONDARY_PBM_BIN, SECONDARY_PBM_PCN, SECONDARY_PBM_GROUP, SECONDARY_PBM_PLAN_ID,\n", "  TERTIARY_COVERA<PERSON>_TYPE, TERTIARY_PAYOR_NAME, TERTIARY_PAYOR_TYPE, TERTIARY_PA_REQUIRED, TERTIARY_PA_AUTH_START,\n", "  TERTIARY_PA_AUTH_END, TERTIARY_PBM_NAME, TERTIARY_PBM_BIN, TERTIARY_PBM_PCN, TERTIARY_PBM_GROUP, TERTIARY_PBM_PLAN_ID,\n", "  CLAIM_TYPE, PATIENT_COPAY_AMT, COPAY_ASSIST_FLAG, COPAY_ASSIST_ID, COPAY_ASSIST_AMT, PATIENT_FINAL_OOP, DATASOURCE_ID,\n", "  REC_DATE_FORMATTED, REFERRAL_DATE_FORMATTED, STATUS_DATE_FORMATTED, PROGRAM_CONSENT_DATE_FORMATTED,\n", "  HIPAA_AUTH_DATE_FORMATTED, MARKETING_CONSENT_DATE_FORMATTED, RX_DATE_FORMATTED, DATE_SHIPPED_FORMATTED,\n", "  PRODUCT_EXP_DATE_FORMATTED, PRIMARY_PA_AUTH_START_FORMATTED, PRIMARY_PA_AUTH_END_FORMATTED,\n", "  SECONDARY_PA_AUTH_START_FORMATTED, SECONDARY_PA_AUTH_END_FORMATTED, TERTIARY_PA_AUTH_START_FORMATTED,\n", "  TERTIARY_PA_AUTH_END_FORMATTED,PATIENT_AGE,LATEST_INDICATION,PATIENT_AGE_ON_RX\n", "FROM silver.curated_prx_patient_status\n", "WHERE IS_PROCESSED = TRUE\n", "  AND PROCESSING_STATUS = 'PROCESSING'\n", "  AND DATE(date_created) = CURRENT_DATE();\n", "\n", "-- mark previous statements as deleted once revised recs recieved\n", "WITH latest_sequence AS (\n", "  SELECT TRANSACTION_ID,TRANSACTION_TYPE,SP_PATIENT_ID,MAX(TRANSACTION_SEQUENCE) AS MAX_SEQ\n", "  FROM gold.PRX_PATIENT_STATUS\n", "  WHERE IS_DELETED = 'FALSE'\n", "  GROUP BY TRANSACTION_ID, TRANSACTION_TYPE, SP_PATIENT_ID\n", ")\n", "UPDATE gold.PRX_PATIENT_STATUS AS a\n", "SET IS_DELETED = 'TRUE'\n", "WHERE EXISTS (\n", "  SELECT 1 FROM latest_sequence AS b\n", "  WHERE a.TRANSACTION_ID = b.TRANSACTION_ID\n", "    AND a.TRANSACTION_TYPE = b.TRANSACTION_TYPE\n", "    AND a.SP_PATIENT_ID = b.SP_PATIENT_ID\n", "    AND a.TRANSACTION_SEQUENCE < b.MAX_SEQ\n", ");\n", "\n", "-- update silver for sucess records\n", "update silver.curated_prx_patient_status set PROCESSING_STATUS = 'COMPLETED'  where IS_PROCESSED = 'TRUE' AND PROCESSING_STATUS = 'PROCESSING' and date(date_created) = current_date();\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 6522258838648596, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "4_clean_gold_dataload", "widgets": {"key": {"currentValue": "456", "nuid": "32c4770a-aa80-419e-ab5a-a42c15eb833f", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "456", "label": "Enter key", "name": "key", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "456", "label": "Enter key", "name": "key", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "source_system_id": {"currentValue": "1", "nuid": "eedef8d8-8d2b-4fc8-a6e8-0d68e24a1a10", "typedWidgetInfo": {"autoCreated": false, "defaultValue": "1", "label": "Enter id", "name": "source_system_id", "options": {"widgetDisplayType": "Text", "validationRegex": null}, "parameterDataType": "String"}, "widgetInfo": {"widgetType": "text", "defaultValue": "1", "label": "Enter id", "name": "source_system_id", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}