-- SELECT 
--   'TRANSACTION_ID contains blank/NULL, non-integer value, or duplicate combination with Transaction_Sequence',
--   'TRANSACTION_ID',
--   cast(transaction_id as string),
--   cast(sp_patient_id as string),
--   cast(transaction_id as string),
--   date_created,
--   id
-- FROM (
--   SELECT 
--     transaction_id,
--     transaction_sequence,
--     sp_patient_id,
--     date_created,
--     id,
--     COUNT(*) OVER (PARTITION BY transaction_id, transaction_sequence) AS txn_count
--   FROM silver.curated_prx_patient_status
--   WHERE is_processed = true
-- ) tmp
-- WHERE 
--   transaction_id IS NULL
--   OR trim(transaction_id) = ''
--   OR cast(transaction_id as int) IS NULL
--   OR txn_count > 1


-- update silver.curated_prx_patient_status set IS_PROCESSED='TRUE',PROCESSING_STATUS='PROCESSING',PROCESSING_ERROR='';
-- <PERSON><PERSON><PERSON> INTO silver.curated_prx_patient_status AS target
-- USING (
--   SELECT 
--     sp_patient_id, 
--     transaction_id, 
--     concat_ws(',', collect_list(error_type)) AS error_type
--   FROM dwh_dqm.prx_dqm_view
--   WHERE DATE(date_created) = CURRENT_DATE() 
--     AND is_critical = 'yes'
--   GROUP BY sp_patient_id, transaction_id
-- ) AS source
-- ON target.sp_patient_id <=> source.sp_patient_id
--    AND target.transaction_id <=> source.transaction_id
--    AND DATE(target.date_created) = CURRENT_DATE()
-- WHEN MATCHED THEN 
--   UPDATE SET 
--     target.IS_PROCESSED = 'FALSE',
--     target.PROCESSING_STATUS = 'ERROR',
--     target.PROCESSING_ERROR = source.error_type;


-- SELECT *
-- FROM dwh_dqm.prx_dqm_view
-- WHERE DATE(date_created) = CURRENT_DATE()
--   AND is_critical = 'yes'
-- LIMIT 1
WITH source AS (
  SELECT 
    sp_patient_id, 
    transaction_id, 
    concat_ws(',', collect_list(error_type)) AS error_type
  FROM dwh_dqm.prx_dqm_view
  WHERE DATE(date_created) = CURRENT_DATE() 
    AND is_critical = 'yes'
  GROUP BY sp_patient_id, transaction_id
),
non_empty_source AS (
  SELECT * FROM source WHERE sp_patient_id IS NOT NULL OR transaction_id IS NOT NULL
)

MERGE INTO silver.curated_prx_patient_status AS target
USING non_empty_source AS source
ON target.sp_patient_id <=> source.sp_patient_id
   AND target.transaction_id <=> source.transaction_id
   AND DATE(target.date_created) = CURRENT_DATE()
WHEN MATCHED THEN 
  UPDATE SET 
    target.IS_PROCESSED = 'FALSE',
    target.PROCESSING_STATUS = 'ERROR',
    target.PROCESSING_ERROR = source.error_type;

--  delete from silver.curated_prx_patient_status;   