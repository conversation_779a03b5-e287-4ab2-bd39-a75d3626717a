[2025-07-08T09:51:05.061+0000] {processor.py:161} INFO - Started process (PID=192) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:05.064+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:51:05.070+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.069+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:05.124+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:05.368+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.368+0000] {override.py:1829} INFO - Created Permission View: can edit on DAG:example_oauth_dag
[2025-07-08T09:51:05.382+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.382+0000] {override.py:1829} INFO - Created Permission View: can delete on DAG:example_oauth_dag
[2025-07-08T09:51:05.392+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.391+0000] {override.py:1829} INFO - Created Permission View: can read on DAG:example_oauth_dag
[2025-07-08T09:51:05.393+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.393+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:51:05.408+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.408+0000] {dag.py:3118} INFO - Creating ORM DAG for example_oauth_dag
[2025-07-08T09:51:05.422+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.422+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:51:05.450+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.403 seconds
[2025-07-08T09:51:35.752+0000] {processor.py:161} INFO - Started process (PID=232) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:35.754+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:51:35.756+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.756+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:35.800+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:35.836+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.836+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:51:35.861+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.861+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:51:35.883+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.140 seconds
[2025-07-08T09:52:06.039+0000] {processor.py:161} INFO - Started process (PID=248) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:06.041+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:52:06.044+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.044+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:06.100+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:06.144+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.144+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:52:06.178+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.177+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:52:06.208+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.179 seconds
[2025-07-08T09:52:30.053+0000] {processor.py:161} INFO - Started process (PID=264) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:30.055+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:52:30.059+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.058+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:30.108+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:30.156+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.155+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:52:30.187+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.186+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:52:30.216+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.171 seconds
[2025-07-08T09:58:22.505+0000] {processor.py:161} INFO - Started process (PID=193) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:58:22.506+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:58:22.509+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:22.509+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:58:22.559+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:58:22.790+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:22.790+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:58:22.814+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:22.813+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:58:22.889+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.392 seconds
[2025-07-08T09:58:52.198+0000] {processor.py:161} INFO - Started process (PID=233) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:58:52.202+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:58:52.208+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:52.208+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:58:52.271+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:58:52.288+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:52.288+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:58:52.311+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:58:52.311+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:58:52.336+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.151 seconds
[2025-07-08T09:59:19.556+0000] {processor.py:161} INFO - Started process (PID=249) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:59:19.558+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:59:19.562+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:19.562+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:59:19.611+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:59:19.652+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:19.651+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:59:19.682+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:19.682+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:59:19.710+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.172 seconds
[2025-07-08T09:59:50.005+0000] {processor.py:161} INFO - Started process (PID=265) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:59:50.007+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:59:50.010+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:50.010+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:59:50.074+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:59:50.109+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:50.109+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:59:50.142+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:59:50.141+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:59:50.170+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.174 seconds
[2025-07-08T10:00:20.678+0000] {processor.py:161} INFO - Started process (PID=281) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:00:20.680+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:00:20.684+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:20.684+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:00:20.735+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:00:20.775+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:20.775+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:00:20.809+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:20.809+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:00:20.846+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.178 seconds
[2025-07-08T10:00:55.778+0000] {processor.py:161} INFO - Started process (PID=298) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:00:55.780+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:00:55.783+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:55.783+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:00:55.847+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:00:55.884+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:55.884+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:00:48.230+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:00:48.229+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:00:48.260+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.187 seconds
[2025-07-08T10:01:20.907+0000] {processor.py:161} INFO - Started process (PID=312) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:01:20.909+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:01:20.914+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:20.913+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:01:13.274+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:01:13.314+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:13.314+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:01:13.344+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:13.344+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:01:13.374+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.159 seconds
[2025-07-08T10:01:38.352+0000] {processor.py:161} INFO - Started process (PID=328) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:01:38.354+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:01:38.358+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:38.357+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:01:38.426+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:01:38.467+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:38.467+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:01:38.504+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:01:38.503+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:01:38.544+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.203 seconds
[2025-07-08T10:02:10.947+0000] {processor.py:161} INFO - Started process (PID=344) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:02:10.951+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:02:10.954+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:10.953+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:02:03.279+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:02:03.311+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:03.311+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:02:03.350+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:03.349+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:02:03.383+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.169 seconds
[2025-07-08T10:02:33.962+0000] {processor.py:161} INFO - Started process (PID=360) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:02:33.964+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:02:33.968+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:33.968+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:02:34.038+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:02:34.084+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:34.083+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:02:34.116+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:02:34.116+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:02:34.148+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.195 seconds
[2025-07-08T10:03:04.715+0000] {processor.py:161} INFO - Started process (PID=376) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:03:04.718+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:03:04.723+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:04.723+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:03:04.762+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:03:04.801+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:04.801+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:03:04.831+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:04.831+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:03:04.857+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.156 seconds
[2025-07-08T10:03:35.451+0000] {processor.py:161} INFO - Started process (PID=392) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:03:35.455+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:03:35.458+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:35.458+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:03:35.514+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:03:35.554+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:35.554+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:03:35.589+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:03:35.589+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:03:35.623+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.181 seconds
[2025-07-08T10:04:03.676+0000] {processor.py:161} INFO - Started process (PID=408) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:04:03.678+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:04:03.681+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:04:03.681+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:04:03.739+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:04:03.776+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:04:03.775+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:04:03.809+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:04:03.809+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:04:03.843+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.175 seconds
[2025-07-08T10:04:33.675+0000] {processor.py:161} INFO - Started process (PID=424) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:04:33.677+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:04:33.679+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:04:33.679+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:04:33.727+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:04:33.764+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:04:33.764+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:04:33.787+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:04:33.787+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:04:33.813+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.145 seconds
[2025-07-08T10:05:04.161+0000] {processor.py:161} INFO - Started process (PID=440) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:04.163+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:05:04.167+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:04.166+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:04.234+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:04.279+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:04.278+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:05:04.313+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:04.313+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:05:04.342+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.193 seconds
[2025-07-08T10:05:36.323+0000] {processor.py:161} INFO - Started process (PID=456) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:36.325+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:05:36.329+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:36.328+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:28.703+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:28.749+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:28.748+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:05:28.782+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:28.782+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:05:28.810+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.195 seconds
[2025-07-08T10:05:58.794+0000] {processor.py:161} INFO - Started process (PID=472) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:58.795+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:05:58.798+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:58.797+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:58.839+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:05:58.869+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:58.869+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:05:58.896+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:05:58.895+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:05:58.920+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.133 seconds
[2025-07-08T10:06:29.154+0000] {processor.py:161} INFO - Started process (PID=496) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:06:29.156+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:06:29.159+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:06:29.158+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:06:29.212+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:06:29.261+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:06:29.260+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:06:29.306+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:06:29.306+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:06:29.382+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.236 seconds
[2025-07-08T10:06:59.974+0000] {processor.py:161} INFO - Started process (PID=510) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:06:59.976+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:06:59.980+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:06:59.980+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:07:00.027+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:07:00.061+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:07:00.060+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:07:00.087+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:07:00.087+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:07:00.121+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.156 seconds
[2025-07-08T10:07:30.916+0000] {processor.py:161} INFO - Started process (PID=526) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T10:07:30.918+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T10:07:30.923+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:07:30.922+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T10:07:30.972+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T10:07:31.015+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:07:31.015+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T10:07:31.055+0000] {logging_mixin.py:188} INFO - [2025-07-08T10:07:31.055+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T10:07:31.090+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.186 seconds
