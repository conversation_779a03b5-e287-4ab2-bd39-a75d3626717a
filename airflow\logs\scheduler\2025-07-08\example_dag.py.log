[2025-07-08T09:51:05.061+0000] {processor.py:161} INFO - Started process (PID=192) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:05.064+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:51:05.070+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.069+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:05.124+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:05.368+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.368+0000] {override.py:1829} INFO - Created Permission View: can edit on DAG:example_oauth_dag
[2025-07-08T09:51:05.382+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.382+0000] {override.py:1829} INFO - Created Permission View: can delete on DAG:example_oauth_dag
[2025-07-08T09:51:05.392+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.391+0000] {override.py:1829} INFO - Created Permission View: can read on DAG:example_oauth_dag
[2025-07-08T09:51:05.393+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.393+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:51:05.408+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.408+0000] {dag.py:3118} INFO - Creating ORM DAG for example_oauth_dag
[2025-07-08T09:51:05.422+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:05.422+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:51:05.450+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.403 seconds
[2025-07-08T09:51:35.752+0000] {processor.py:161} INFO - Started process (PID=232) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:35.754+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:51:35.756+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.756+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:35.800+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:51:35.836+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.836+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:51:35.861+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:51:35.861+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:51:35.883+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.140 seconds
[2025-07-08T09:52:06.039+0000] {processor.py:161} INFO - Started process (PID=248) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:06.041+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:52:06.044+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.044+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:06.100+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:06.144+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.144+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:52:06.178+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:06.177+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:52:06.208+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.179 seconds
[2025-07-08T09:52:30.053+0000] {processor.py:161} INFO - Started process (PID=264) to work on /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:30.055+0000] {processor.py:830} INFO - Processing file /opt/airflow/dags/example_dag.py for tasks to queue
[2025-07-08T09:52:30.059+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.058+0000] {dagbag.py:545} INFO - Filling up the DagBag from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:30.108+0000] {processor.py:840} INFO - DAG(s) 'example_oauth_dag' retrieved from /opt/airflow/dags/example_dag.py
[2025-07-08T09:52:30.156+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.155+0000] {dag.py:3096} INFO - Sync 1 DAGs
[2025-07-08T09:52:30.187+0000] {logging_mixin.py:188} INFO - [2025-07-08T09:52:30.186+0000] {dag.py:3954} INFO - Setting next_dagrun for example_oauth_dag to 2025-07-07 00:00:00+00:00, run_after=2025-07-08 00:00:00+00:00
[2025-07-08T09:52:30.216+0000] {processor.py:183} INFO - Processing /opt/airflow/dags/example_dag.py took 0.171 seconds
