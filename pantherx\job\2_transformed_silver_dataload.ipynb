{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bfe15c5a-7df9-46e5-bb02-3183e70540e5", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import (lit, current_timestamp, col, to_timestamp, to_date, date_format)\n", "from pyspark.sql.window import Window\n", "\n", "bronze_df = spark.read \\\n", "            .option(\"header\", \"true\") \\\n", "            .option(\"delimiter\", \"|\") \\\n", "            .option(\"multiline\", \"false\") \\\n", "            .option(\"escape\", '\"') \\\n", "            .option(\"quote\", '\"') \\\n", "            .option(\"inferSchema\", \"false\") \\\n", "            .format(\"delta\").load(\"/mnt/pantherx/bronze/raw_prx_patient_status\")\n", "\n", "bronze_df = bronze_df.drop(\"DATE_CREATED\")\n", "\n", "bronze_df = bronze_df.withColumn(\"REC_DATE_FORMATTED\",\n", "        date_format(to_timestamp(col(\"REC_DATE\"), \"yyyyMMddHHmmss\"), \"yyyy-MM-dd HH:mm:ss.SSS\")) \\\n", "    .withColumn(\"REFERRAL_DATE_FORMATTED\",\n", "        date_format(to_date(col(\"REFERRAL_DATE\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"STATUS_DATE_FORMATTED\", \n", "        date_format(to_timestamp(col(\"status_date\"), \"yyyyMMddHHmmss\"), \"yyyy-MM-dd HH:mm:ss.SSS\")) \\\n", "    .withColumn(\"PROGRAM_CONSENT_DATE_FORMATTED\", \n", "        date_format(to_date(col(\"PROGRAM_CONSENT_DATE\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"HIPAA_AUTH_DATE_FORMATTED\", \n", "        date_format(to_date(col(\"HIPAA_AUTH_DATE\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"MARKETING_CONSENT_DATE_FORMATTED\", \n", "        date_format(to_date(col(\"MARKETING_CONSENT_DATE\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"RX_DATE_FORMATTED\", \n", "        date_format(to_date(col(\"RX_DATE\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"DATE_SHIPPED_FORMATTED\", \n", "        date_format(to_date(col(\"DATE_SHIPPED\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"PRODUCT_EXP_DATE_FORMATTED\", \n", "        date_format(to_date(col(\"PRODUCT_EXP_DATE\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"PRIMARY_PA_AUTH_START_FORMATTED\", \n", "        date_format(to_date(col(\"PRIMARY_PA_AUTH_START\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"PRIMARY_PA_AUTH_END_FORMATTED\", \n", "        date_format(to_date(col(\"PRIMARY_PA_AUTH_END\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"SECONDARY_PA_AUTH_START_FORMATTED\", \n", "        date_format(to_date(col(\"SECONDARY_PA_AUTH_START\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"SECONDARY_PA_AUTH_END_FORMATTED\", \n", "        date_format(to_date(col(\"SECONDARY_PA_AUTH_END\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"TERTIARY_PA_AUTH_START_FORMATTED\", \n", "        date_format(to_date(col(\"TERTIARY_PA_AUTH_START\"), \"yyyyMMdd\"), \"yyyy-MM-dd\")) \\\n", "    .withColumn(\"TERTIARY_PA_AUTH_END_FORMATTED\", \n", "        date_format(to_date(col(\"TERTIARY_PA_AUTH_END\"), \"yyyyMMdd\"), \"yyyy-MM-dd\"))\n", "\n", "\n", "# bronze_df.createOrReplaceTempView(\"temp_raw_data\")\n", "spark.sql(\"\"\" delete from silver.temp_raw_data_table \"\"\")\n", "bronze_df.write.mode(\"overwrite\").saveAsTable(\"silver.temp_raw_data_table\")\n", "\n", "spark.sql(\"delete from silver.curated_prx_patient_status where date(date_created) = current_date()\")\n", "spark.sql(\"\"\"\n", "    INSERT INTO TABLE silver.curated_prx_patient_status (\n", "  REC_<PERSON>AT<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CODE,PHARMACY_NPI,TRANSACTION_TYPE,TRANSACTION_ID,TRANSACTION_SEQUENCE,PROGRAM_NAME,\n", "  PATIENT_FIRST_NAME, PATIENT_LAST_NAME, REFERRAL_SOURCE, REFERRAL_DATE, HUB_PATIENT_ID, SP_PATIENT_ID,\n", "  PATIENT_DOB, PATIENT_<PERSON>NDER, PATIENT_WEIGHT_KG, PATIENT_ADDRESS_LINE1, PATIENT_ADDRESS_LINE2, PATIENT_CITY,\n", "  PATIENT_STATE, PATIENT_ZIP, PATIENT_PHONE, PATIENT_ALT_PHONE, PATIENT_EMAIL, PATIENT_OK_FOR_MESSAGE, CAREGIVER_NAME,\n", "  CAREGIVER_PHON<PERSON>, CAREGIVER_EMAIL, CAREGIVER_OK_FOR_MESSAGE, PATIENT_EAP, DIAG_CODE_1, DIAG_CODE_2,\n", "  GENETIC_TEST_COMPLETED, GENETIC_TEST_RESULT, FAMILY_HISTORY_OF_WHIM, CURRENT_THERAPIES, PREVIOUS_THERAPIES,\n", "  STATUS_DATE, PRO<PERSON><PERSON>_CONSENT, PROGRAM_CONSENT_DATE, HIPAA_AUTH_STATUS, HIPAA_AUTH_DATE, MARKETING_CONSENT_STATUS,\n", "  MARKETING_CONSENT_DATE, PRESCRIBER_LAST_NAME, PRESCRIBER_FIRST_NAME, PRESCRIBER_ADDRESS_LINE1, PRESCRIBER_ADDRESS_LINE2, PRESCRIBER_CITY, PRESCRIBER_STATE, PRESCRIBER_ZIP, PRESCRIBER_PHONE, PRESCRIBER_FAX,\n", "  PRESCRIBER_NPI, PRESCRIBER_DEA, PRESCRIBER_SPECIALTY, FACILITY_NAME, CONTACT_NAME, CONTACT_EMAIL,\n", "  HCP_SIGNATURE_ON_NEW_RX, RX_DATE, RX_NUMBER, RX_REFILLS_ALLOWED, RX_FILL_NUMBER, REFILLS_REMAINING, DAW, NDC,\n", "  PRODUCT_NAME, <PERSON>U<PERSON><PERSON>TY_DISPENSED, TOTAL_DAILY_DOSE, UNIT_OF_MEASURE, DAYS_SUPPLY, DATE_SHIPPED, PRODUCT_LOT,\n", "  PRODUCT_EXP_DATE,SHIP_TO_LOCATION,SHIP_CARRIER,SHIP_TRACKING_NO,PRIMARY_COVERAGE_TYPE,PRIMARY_PAYOR_NAME,\n", "  PRIMARY_PAYOR_SEGMENT, PRIMARY_PA_REQUIRED, PRIMARY_PA_AUTH_START, PRIMARY_PA_AUTH_END, PRIMARY_PBM_NAME,\n", "  PRIMARY_P<PERSON>_<PERSON>IN, PRIMAR<PERSON>_PBM_PCN, PR<PERSON>AR<PERSON>_<PERSON><PERSON>_GROUP, PRIMARY_PBM_PLAN_ID, SECONDARY_COVERAGE_TYPE, SECONDARY_PAYOR_NAME, SECONDARY_PAYOR_TYPE, SECONDARY_PA_REQUIRED, SECONDARY_PA_AUTH_START, SECONDARY_PA_AUTH_END,\n", "  SECONDARY_PBM_NAME, SECONDARY_PBM_BIN, SECONDARY_PBM_PCN, SECONDARY_PBM_GROUP, SECONDARY_PBM_PLAN_ID,\n", "  TERTIARY_COVERA<PERSON>_TYPE, TERTIARY_PAYOR_NAME, TERTIARY_PAYOR_TYPE, TERTIARY_PA_REQUIRED, TERTIARY_PA_AUTH_START,\n", "  TERTIARY_PA_AUTH_END, TERTIARY_PBM_NAME, TERTIARY_PBM_BIN, TERTIARY_PBM_PCN, TERTIARY_PBM_GROUP, TERTIARY_PBM_PLAN_ID,\n", "  CLAIM_TYPE, PATIENT_COPAY_AMT, COPAY_ASSIST_FLAG, COPAY_ASSIST_ID, COPAY_ASSIST_AMT, PATIENT_FINAL_OOP, DATASOURCE_ID,\n", "  REC_DATE_FORMATTED, REFERRAL_DATE_FORMATTED, STATUS_DATE_FORMATTED, PROGRAM_CONSENT_DATE_FORMATTED,\n", "  HIPAA_AUTH_DATE_FORMATTED, MARKETING_CONSENT_DATE_FORMATTED, RX_DATE_FORMATTED, DATE_SHIPPED_FORMATTED,\n", "  PRODUCT_EXP_DATE_FORMATTED, PRIMARY_PA_AUTH_START_FORMATTED, PRIMARY_PA_AUTH_END_FORMATTED,\n", "  SECONDARY_PA_AUTH_START_FORMATTED, SECONDARY_PA_AUTH_END_FORMATTED, TERTIARY_PA_AUTH_START_FORMATTED,\n", "  TERTIARY_PA_AUTH_END_FORMATTED\n", ") \n", "SELECT REC_DATE, PHA<PERSON><PERSON><PERSON><PERSON>_CODE, PHAR<PERSON>CY_<PERSON>PI, TRANSACTION_TYPE, TRANSACTION_ID, TRANSACTION_SEQUENCE, PROGRAM_NAME,\n", "  PATIENT_FIRST_NAME, PATIENT_LAST_NAME,REFERRAL_SOURCE,REFERRAL_DATE,HUB_PATIENT_ID,SP_PATIENT_ID,PATIENT_DOB,\n", "  PATIENT_<PERSON>ND<PERSON>,PATIENT_WEIGHT_KG,PATIENT_ADDRESS_LINE1,PATIENT_ADDRESS_LINE2,PATIENT_CITY,PATIENT_STATE,PATIENT_ZIP,PATIENT_PHONE,PATIENT_ALT_PHONE,PATIENT_EMAIL,PATIENT_OK_FOR_MESSAGE,CAREGIVER_NAME,CAREGIVER_PHONE,CAREGIVER_EMAIL,\n", "  CAREGIVER_OK_FOR_MESSAGE,PATIENT_EAP,DIAG_CODE_1,DIAG_CODE_2,GENETIC_TEST_COMPLETED,GENETIC_TEST_RESULT,\n", "  FAMILY_HISTORY_OF_WHIM,CURRENT_THERAPIES,PREVIOUS_THERAPIES,STATUS_DATE,PROGRAM_CONSENT,PROGRAM_CONSENT_DATE,\n", "  HIPAA_AUTH_STATUS,HIPAA_AUTH_DATE,MARKETING_CONSENT_STATUS,MARKETING_CONSENT_DATE,PRESCRIBER_LAST_NAME,\n", "  PRESCRIBER_FIRST_NAME,PRESCRIBER_ADDRESS_LINE1,PRESCRIBER_ADDRESS_LINE2,PRESCRIBER_CITY,PRESCRIBER_STATE,PRESCRIBER_ZIP,\n", "  PRESCRIBER_PHONE, PRESCRIBER_FAX, PRESCRIBER_NPI, PRESCRIBER_DEA, PRESCRIBER_SPECIALTY, FACILITY_NAME, CONTACT_NAME,\n", "  CONTACT_EMAIL, HCP_SIGNATURE_ON_NEW_RX,RX_DATE, RX_NUMBER, RX_REFILLS_ALLOWED, RX_FILL_NUMBER, REFIL<PERSON>_REMAINING,\n", "  DAW, NDC, PRODUCT_NAME, QUANTITY_DISPENSED, TOTAL_DAILY_DOSE, UNIT_OF_MEASURE, DAYS_SUPPLY, DATE_SHIPPED, PRODUCT_LOT,\n", "  PRODUCT_EXP_DATE, SHIP_TO_LOCATION, SHIP_CARRIER, SHIP_TRACKING_NO, PRIMARY_COVERAGE_TYPE, PRIMARY_PAYOR_NAME,\n", "  PRIMARY_PAYOR_SEGMENT, PRIMARY_PA_REQUIRED, PRIMARY_PA_AUTH_START, PRIMARY_PA_AUTH_END, PRIMARY_PBM_NAME,\n", "  PRIMARY_P<PERSON>_BIN, PRIMARY_PBM_PCN, PRIMARY_PBM_GROUP, PRIMARY_PBM_PLAN_ID, SECONDARY_COVERAGE_TYPE, SECONDARY_PAYOR_NAME,\n", "  SECONDARY_PAYOR_TYPE, SECONDARY_PA_REQUIRED, SECONDARY_PA_AUTH_START, SECONDARY_PA_AUTH_END, SECONDARY_PBM_NAME,\n", "  SECONDARY_P<PERSON>_BIN, SECONDARY_PBM_PCN, SECONDARY_PBM_GROUP, SECONDARY_PBM_PLAN_ID, TERTIARY_COVERAGE_TYPE,\n", "  TERTIARY_PAYOR_NAME,TERTIARY_PAYOR_TYPE,TERTIARY_PA_REQUIRED,TERTIARY_PA_AUTH_START,TERTIARY_PA_AUTH_END,\n", "  TERTIARY_P<PERSON>_NAME, TERTIARY_PBM_BIN, TERTIARY_PBM_PCN, TERTIARY_PBM_GROUP, TERTIARY_PBM_PLAN_ID, CLAIM_TYPE, PATIENT_COPAY_AMT,COPAY_ASSIST_FLAG,COPAY_ASSIST_ID,COPAY_ASSIST_AMT,PATIENT_FINAL_OOP,DATASOURCE_ID,REC_DATE_FORMATTED,\n", "  REFERRAL_DATE_FORMATTED,STATUS_DATE_FORMATTED,PROGRAM_CONSENT_DATE_FORMATTED,HIPAA_AUTH_DATE_FORMATTED,\n", "  MARKETING_CONSENT_DATE_FORMATTED,RX_DATE_FORMATTED,DATE_SHIPPED_FORMATTED,PRODUCT_EXP_DATE_FORMATTED,\n", "  PRIMARY_PA_AUTH_START_FORMATTED,PRIMARY_PA_AUTH_END_FORMATTED,SECONDARY_PA_AUTH_START_FORMATTED,SECONDARY_PA_AUTH_END_FORMATTED,TERTIARY_PA_AUTH_START_FORMATTED,TERTIARY_PA_AUTH_END_FORMATTED\n", " FROM silver.temp_raw_data_table\n", "\"\"\")    "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b4a8d52a-b820-4df3-b8da-bb6e570462de", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import sys\n", "from pyspark.sql.types import IntegerType\n", "sys.path.append(\"/Workspace/Users/<USER>/Data%20Engineering_Automation/pantherx/udf/\")\n", "from age_calculations import calculate_age, calculate_age_by_rx_date\n", "\n", "calculate_age_udf = udf(calculate_age, IntegerType())\n", "spark.udf.register(\"calculate_age\", calculate_age_udf)\n", "\n", "calculate_age_by_rx_date_udf = udf(calculate_age_by_rx_date, IntegerType())\n", "spark.udf.register(\"calculate_age_by_rx_date\", calculate_age_by_rx_date_udf)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6bed1882-00bd-40f7-81f2-0012896b4052", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "-- status update\n", "MERGE INTO silver.curated_prx_patient_status AS ps\n", "USING (\n", "    WITH upd AS (\n", "        SELECT \n", "            scat.id, \n", "            staged.SP_PATIENT_ID AS sp_id, \n", "            staged.TRANSACTION_ID AS t_id\n", "        FROM silver.temp_raw_data_table AS staged \n", "        LEFT JOIN dwh_utils.status_catalog scat \n", "            ON UPPER(staged.STATUS) = UPPER(scat.status)\n", "    )\n", "    SELECT id, sp_id, t_id \n", "    FROM upd \n", "    GROUP BY id, sp_id, t_id\n", ") AS cte\n", "ON ps.sp_patient_id = cte.sp_id \n", "   AND ps.TRANSACTION_ID = cte.t_id\n", "   AND DATE(ps.date_created) = CURRENT_DATE()\n", "WHEN MATCHED THEN\n", "    UPDATE SET \n", "        ps.status = cte.id,\n", "        ps.DATE_LAST_UPDATED = current_timestamp();\n", "\n", "-- sub_status update\n", "MERGE INTO silver.curated_prx_patient_status AS ps\n", "USING (\n", "    WITH upd AS (\n", "        SELECT \n", "            ssc.ID, \n", "            staged.SP_PATIENT_ID AS sp_id, \n", "            staged.TRANSACTION_ID AS t_id,\n", "            ROW_NUMBER() OVER (PARTITION BY staged.SP_PATIENT_ID, staged.TRANSACTION_ID ORDER BY staged.SP_PATIENT_ID) AS rn\n", "        FROM silver.temp_raw_data_table AS staged \n", "        LEFT JOIN silver.curated_prx_patient_status ppss on  ppss.transaction_id=staged.TRANSACTION_ID  \n", "            left join dwh_utils.sub_status_catalog ssc on  ssc.status_id=ppss.status and UPPER(ssc.sub_status)\n", "            =UPPER(staged.SUB_STATUS)\n", "    )\n", "    SELECT id, sp_id, t_id \n", "    FROM upd where rn=1\n", "    GROUP BY id, sp_id, t_id\n", ") AS cte\n", "ON ps.sp_patient_id = cte.sp_id \n", "   AND ps.TRANSACTION_ID = cte.t_id\n", "   AND DATE(ps.date_created) = CURRENT_DATE()\n", "WHEN MATCHED THEN\n", "    UPDATE SET \n", "        ps.sub_status = cte.id,\n", "        ps.DATE_LAST_UPDATED = current_timestamp();\n", "\n", "-- zip updates\n", "UPDATE silver.curated_prx_patient_status LR\n", "              SET prescriber_zip = CASE WHEN LENGTH(LR.prescriber_zip) > 5 THEN LEFT(LR.prescriber_zip, 5) END\n", "              WHERE LENGTH(LR.prescriber_zip) > 5;\n", "\n", "UPDATE silver.curated_prx_patient_status LR\n", "              SET PATIENT_ZIP = CASE WHEN LENGTH(LR.PATIENT_ZIP) > 5 THEN LEFT(LR.PATIENT_ZIP, 5) END\n", "              WHERE LENGTH(LR.PATIENT_ZIP) > 5;\n", "\n", "-- update latest_indication\n", "MERGE INTO silver.curated_prx_patient_status AS ps\n", "USING (\n", "    WITH upd AS (\n", "        SELECT\n", "            SP_PATIENT_ID, \n", "            DIAG_CODE_1 AS LATEST_INDICATION, \n", "            STATUS_DATE_FORMATTED AS STATUS_DATE_FORMATTED,\n", "            DENSE_RANK() OVER (\n", "                PARTITION BY SP_PATIENT_ID \n", "                ORDER BY STATUS_DATE_FORMATTED DESC\n", "            ) AS DR\n", "        FROM silver.curated_prx_patient_status\n", "        WHERE DIAG_CODE_1 IS NOT NULL \n", "          AND IS_PROCESSED = TRUE\n", "    ),\n", "    deduped AS (\n", "        SELECT \n", "            SP_PATIENT_ID, \n", "            LATEST_INDICATION,\n", "            ROW_NUMBER() OVER (\n", "                PARTITION BY SP_PATIENT_ID \n", "                ORDER BY STATUS_DATE_FORMATTED DESC\n", "            ) AS RN\n", "        FROM upd\n", "        WHERE DR = 1\n", "    )\n", "    SELECT SP_PATIENT_ID, LATEST_INDICATION\n", "    FROM deduped\n", "    WHERE RN = 1\n", ") AS cte\n", "ON ps.SP_PATIENT_ID = cte.SP_PATIENT_ID\n", "   AND DATE(ps.date_created) = CURRENT_DATE()\n", "WHEN MATCHED THEN\n", "  UPDATE SET \n", "    ps.LATEST_INDICATION = cte.LATEST_INDICATION,\n", "    ps.DATE_LAST_UPDATED = current_timestamp();\n", "\n", "-- update patient_age and patient_age_on_rx\n", "MERGE INTO silver.CURATED_PRX_PATIENT_STATUS AS PRX\n", "USING (\n", "    SELECT \n", "        SP_PATIENT_ID,\n", "        calculate_age(PATIENT_DOB) AS CALC_PATIENT_AGE,\n", "        calculate_age_by_rx_date(PATIENT_DOB, RX_DATE) AS CALC_PATIENT_AGE_ON_RX\n", "    FROM (\n", "        SELECT\n", "            \n", "            SP_PATIENT_ID,\n", "            PATIENT_DOB,\n", "            RX_DATE,\n", "            ROW_NUMBER() OVER (\n", "                PARTITION BY SP_PATIENT_ID  \n", "                ORDER BY STATUS_DATE_FORMATTED DESC, TRANSACTION_SEQUENCE DESC\n", "            ) AS rn\n", "        FROM silver.CURATED_PRX_PATIENT_STATUS\n", "    ) AS ranked\n", "    WHERE ranked.rn = 1\n", ") AS LR\n", "ON PRX.SP_PATIENT_ID = LR.SP_PATIENT_ID \n", "WHEN MATCHED THEN\n", "UPDATE SET \n", "    PRX.PATIENT_AGE = LR.CALC_PATIENT_AGE,\n", "    PRX.PATIENT_AGE_ON_RX = LR.CALC_PATIENT_AGE_ON_RX;\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "inputWidgetPreferences": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 5431991002013572, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "2_transformed_silver_dataload", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}