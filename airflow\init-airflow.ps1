# PowerShell script to initialize Airflow manually if needed

Write-Host "Initializing Airflow database..."
docker-compose exec airflow-webserver airflow db init

Write-Host "Creating admin user..."
docker-compose exec airflow-webserver airflow users create `
  --username admin `
  --firstname Admin `
  --lastname User `
  --role Admin `
  --email <EMAIL> `
  --password admin123

Write-Host "Airflow initialization complete!"
Write-Host "You can now access Airflow at http://localhost:8080"
Write-Host "Username: admin"
Write-Host "Password: admin123"
